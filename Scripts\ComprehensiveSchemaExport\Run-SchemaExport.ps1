#Requires -Version 5.1
<#
.SYNOPSIS
    Comprehensive Schema Export Tool - PowerShell Runner
    
.DESCRIPTION
    PowerShell script to run the comprehensive schema export tool with proper error handling
    and environment setup. Supports both interactive and automated execution.
    
.PARAMETER ConfigFile
    Optional path to JSON configuration file
    
.PARAMETER OutputFormat
    Output format(s): json, xml, txt, or all (default: all)
    
.PARAMETER SampleSize
    Number of sample rows to export per table (default: 10)
    
.PARAMETER BackupToDesktop
    Create backup copies on desktop (default: true)

.PARAMETER ExcludeSchemas
    Comma-separated list of schemas to exclude (default: system schemas)

.PARAMETER IncludeSchemas
    Comma-separated list of schemas to include (overrides exclude list)

.PARAMETER SchemaFilterMode
    Schema filtering mode: 'exclude', 'include', or 'all' (default: exclude)

.EXAMPLE
    .\Run-SchemaExport.ps1
    Run with default settings (excludes system schemas)

.EXAMPLE
    .\Run-SchemaExport.ps1 -OutputFormat json -SampleSize 20
    Export only JSON format with 20 sample rows per table

.EXAMPLE
    .\Run-SchemaExport.ps1 -IncludeSchemas "dbo,custom" -SchemaFilterMode include
    Export only dbo and custom schemas

.EXAMPLE
    .\Run-SchemaExport.ps1 -ExcludeSchemas "sys,temp,backup" -SchemaFilterMode exclude
    Exclude specific schemas

.EXAMPLE
    .\Run-SchemaExport.ps1 -BackupToDesktop:$false -SchemaFilterMode all
    Run without desktop backups and include all schemas
#>

[CmdletBinding()]
param(
    [string]$ConfigFile,
    [ValidateSet('json', 'xml', 'txt', 'all')]
    [string]$OutputFormat = 'all',
    [int]$SampleSize = 10,
    [bool]$BackupToDesktop = $true,
    [string]$ExcludeSchemas,
    [string]$IncludeSchemas,
    [ValidateSet('exclude', 'include', 'all')]
    [string]$SchemaFilterMode = 'exclude'
)

# Set error action preference
$ErrorActionPreference = 'Stop'

# Function to write colored output
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = 'White'
    )
    Write-Host $Message -ForegroundColor $Color
}

# Function to check Python installation
function Test-PythonInstallation {
    try {
        $pythonVersion = python --version 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✓ Python found: $pythonVersion" -Color Green
            return $true
        }
    }
    catch {
        Write-ColorOutput "✗ Python not found in PATH" -Color Red
        return $false
    }
    return $false
}

# Function to check and install requirements
function Install-Requirements {
    Write-ColorOutput "Checking Python requirements..." -Color Yellow
    
    try {
        python -c "import pyodbc" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✓ pyodbc already installed" -Color Green
            return $true
        }
    }
    catch {
        # pyodbc not installed, try to install
    }
    
    Write-ColorOutput "Installing requirements..." -Color Yellow
    try {
        python -m pip install -r requirements.txt
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✓ Requirements installed successfully" -Color Green
            return $true
        }
        else {
            Write-ColorOutput "✗ Failed to install requirements" -Color Red
            return $false
        }
    }
    catch {
        Write-ColorOutput "✗ Error installing requirements: $_" -Color Red
        return $false
    }
}

# Function to create temporary config if parameters provided
function New-TempConfig {
    $needsConfig = ($OutputFormat -ne 'all' -or $SampleSize -ne 10 -or $BackupToDesktop -ne $true -or
                   $ExcludeSchemas -or $IncludeSchemas -or $SchemaFilterMode -ne 'exclude')

    if ($needsConfig) {
        $formats = if ($OutputFormat -eq 'all') { @('json', 'xml', 'txt') } else { @($OutputFormat) }

        $tempConfig = @{
            output_formats = $formats
            sample_size = $SampleSize
            backup_to_desktop = $BackupToDesktop
            schema_filter_mode = $SchemaFilterMode
        }

        # Add schema filtering if specified
        if ($IncludeSchemas) {
            $tempConfig.included_schemas = $IncludeSchemas -split ','
            $tempConfig.schema_filter_mode = 'include'
        }
        elseif ($ExcludeSchemas) {
            $tempConfig.excluded_schemas = $ExcludeSchemas -split ','
            $tempConfig.schema_filter_mode = 'exclude'
        }

        $configPath = "temp_config.json"
        $tempConfig | ConvertTo-Json | Out-File -FilePath $configPath -Encoding UTF8
        Write-ColorOutput "Created temporary configuration: $configPath" -Color Cyan

        # Show schema filtering info
        if ($tempConfig.schema_filter_mode -eq 'include' -and $tempConfig.included_schemas) {
            Write-ColorOutput "Including schemas: $($tempConfig.included_schemas -join ', ')" -Color Cyan
        }
        elseif ($tempConfig.schema_filter_mode -eq 'exclude' -and $tempConfig.excluded_schemas) {
            Write-ColorOutput "Excluding schemas: $($tempConfig.excluded_schemas -join ', ')" -Color Cyan
        }
        elseif ($tempConfig.schema_filter_mode -eq 'all') {
            Write-ColorOutput "Including all schemas (no filtering)" -Color Yellow
        }

        return $configPath
    }
    return $null
}

# Main execution
try {
    Write-ColorOutput "=" * 60 -Color Cyan
    Write-ColorOutput "COMPREHENSIVE SCHEMA EXPORT TOOL" -Color Cyan
    Write-ColorOutput "=" * 60 -Color Cyan
    Write-Host ""
    
    # Check Python
    if (-not (Test-PythonInstallation)) {
        Write-ColorOutput "Please install Python 3.7+ and ensure it's in your PATH" -Color Red
        exit 1
    }
    
    # Install requirements
    if (-not (Install-Requirements)) {
        Write-ColorOutput "Failed to install required packages" -Color Red
        exit 1
    }
    
    # Create temp config if needed
    $tempConfigPath = New-TempConfig
    
    # Prepare Python command
    $pythonArgs = @("comprehensive_schema_export.py")
    if ($ConfigFile) {
        $pythonArgs += "--config", $ConfigFile
    }
    elseif ($tempConfigPath) {
        $pythonArgs += "--config", $tempConfigPath
    }
    
    Write-ColorOutput "Starting schema export..." -Color Yellow
    Write-Host ""
    
    # Run the export
    $startTime = Get-Date
    & python @pythonArgs
    $endTime = Get-Date
    $duration = $endTime - $startTime
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-ColorOutput "✓ Schema export completed successfully!" -Color Green
        Write-ColorOutput "Duration: $($duration.ToString('mm\:ss'))" -Color Green
        
        # List generated files
        $exportFiles = Get-ChildItem -Path . -Filter "*schema_export*" | Where-Object { $_.LastWriteTime -gt $startTime }
        if ($exportFiles) {
            Write-ColorOutput "Generated files:" -Color Cyan
            foreach ($file in $exportFiles) {
                Write-ColorOutput "  ✓ $($file.Name)" -Color White
            }
        }
        
        # Check for desktop backups
        if ($BackupToDesktop) {
            $desktopPath = [Environment]::GetFolderPath("Desktop")
            $desktopFiles = Get-ChildItem -Path $desktopPath -Filter "*schema_export*" | Where-Object { $_.LastWriteTime -gt $startTime }
            if ($desktopFiles) {
                Write-ColorOutput "Desktop backups created:" -Color Cyan
                foreach ($file in $desktopFiles) {
                    Write-ColorOutput "  ✓ $($file.Name)" -Color White
                }
            }
        }
    }
    else {
        Write-ColorOutput "✗ Schema export failed" -Color Red
        Write-ColorOutput "Check schema_export.log for details" -Color Yellow
        exit 1
    }
}
catch {
    Write-ColorOutput "✗ Error: $_" -Color Red
    exit 1
}
finally {
    # Clean up temp config
    if ($tempConfigPath -and (Test-Path $tempConfigPath)) {
        Remove-Item $tempConfigPath -Force
    }
}

Write-Host ""
Write-ColorOutput "=" * 60 -Color Cyan
