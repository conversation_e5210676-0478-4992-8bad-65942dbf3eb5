<?xml version='1.0' encoding='utf-8'?>
<DatabaseSchema><Metadata><exporttimestamp>2025-06-30T17:10:17.433729</exporttimestamp><databaseserver>REMD-ZS-JV6GJV2\SQLEXPRESS</databaseserver><databasename>CompleteViewVms</databasename><exportversion>2.1</exportversion><totaltablesfound>20</totaltablesfound><tablessampled>20</tablessampled><maxtablerowsforsampling>1000000</maxtablerowsforsampling><maxtablerowsforstats>10000000</maxtablerowsforstats><schemafiltermode>exclude</schemafiltermode><aifeaturesenabled>{'system_stats': True, 'column_patterns': True, 'relationship_detection': True, 'ai_hints': True, 'query_examples': True}</aifeaturesenabled></Metadata><SchemaInformation><EnhancedTablesandColumns><Item><FullTableName>cam.AdvancedSettings</FullTableName><COLUMNNAME>AdvancedSettingId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.AdvancedSettings</FullTableName><COLUMNNAME>VideoEncoderType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.AdvancedSettings</FullTableName><COLUMNNAME>VideoDecoderType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.Camera360Lens</FullTableName><COLUMNNAME>Camera360LensId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.Camera360Lens</FullTableName><COLUMNNAME>DewarpLensType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.Camera360Lens</FullTableName><COLUMNNAME>DewarpLensOrientationType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.Camera360Lens</FullTableName><COLUMNNAME>DewarpImmervisionLensProfileType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.Camera360Lens</FullTableName><COLUMNNAME>DewarpViewType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.Camera360Lens</FullTableName><COLUMNNAME>FishEyeCenterX</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.Camera360Lens</FullTableName><COLUMNNAME>FishEyeCenterY</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.Camera360Lens</FullTableName><COLUMNNAME>FishEyeRadius</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.Cameras</FullTableName><COLUMNNAME>CameraId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.Cameras</FullTableName><COLUMNNAME>CameraGuid</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.Cameras</FullTableName><COLUMNNAME>RecordingServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>cam.Cameras</FullTableName><COLUMNNAME>VideoDeviceId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>cam.Cameras</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.Cameras</FullTableName><COLUMNNAME>Name</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>300</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.Cameras</FullTableName><COLUMNNAME>TimeZone</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>40</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.Cameras</FullTableName><COLUMNNAME>CameraNo</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.Cameras</FullTableName><COLUMNNAME>ChannelNo</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.Cameras</FullTableName><COLUMNNAME>Latitude</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>20</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.Cameras</FullTableName><COLUMNNAME>Longitude</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>20</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>11</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.Cameras</FullTableName><COLUMNNAME>AlarmPresetType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>12</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.Cameras</FullTableName><COLUMNNAME>MotionSensitivity</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>13</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.Cameras</FullTableName><COLUMNNAME>AdvancedSettingId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>14</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>cam.Cameras</FullTableName><COLUMNNAME>EdgeSettingId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>15</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>cam.Cameras</FullTableName><COLUMNNAME>RecordingSettingId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>16</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>cam.Cameras</FullTableName><COLUMNNAME>Camera360LensId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>17</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>cam.Cameras</FullTableName><COLUMNNAME>IpSettingId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>18</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>cam.Cameras</FullTableName><COLUMNNAME>StretchSettingId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>19</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>cam.Cameras</FullTableName><COLUMNNAME>PtzSettingId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>20</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>cam.Cameras</FullTableName><COLUMNNAME>StreamProcessingId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>21</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>cam.Cameras</FullTableName><COLUMNNAME>ConeId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>22</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>cam.Cameras</FullTableName><COLUMNNAME>Note</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>23</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.CameraVolumes</FullTableName><COLUMNNAME>CameraVolumeId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.CameraVolumes</FullTableName><COLUMNNAME>CameraId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>cam.CameraVolumes</FullTableName><COLUMNNAME>VolumeId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>cam.CamEvents</FullTableName><COLUMNNAME>CamEventId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.CamEvents</FullTableName><COLUMNNAME>CameraId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>cam.CamEvents</FullTableName><COLUMNNAME>CameraEventId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>cam.CamEvents</FullTableName><COLUMNNAME>OnEventType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.CamEvents</FullTableName><COLUMNNAME>OffEventType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.CamEvents</FullTableName><COLUMNNAME>EventAlias</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.CamEvents</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT>((1))</COLUMNDEFAULT><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.Cones</FullTableName><COLUMNNAME>ConeId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.Cones</FullTableName><COLUMNNAME>RotateAngle</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.Cones</FullTableName><COLUMNNAME>WidthAngle</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.Cones</FullTableName><COLUMNNAME>Depth</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.EdgeSettings</FullTableName><COLUMNNAME>EdgeSettingId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.EdgeSettings</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.EdgeSettings</FullTableName><COLUMNNAME>EdgeStorageType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.EdgeSettings</FullTableName><COLUMNNAME>WillFormatSdCard</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.EdgeSettings</FullTableName><COLUMNNAME>NasAddress</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.EdgeSettings</FullTableName><COLUMNNAME>NasPath</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>1024</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.EdgeSettings</FullTableName><COLUMNNAME>NasUserName</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>64</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.EdgeSettings</FullTableName><COLUMNNAME>NasPassword</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.EdgeSettings</FullTableName><COLUMNNAME>SyncStartTime</COLUMNNAME><DATATYPE>datetime</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.EdgeSettings</FullTableName><COLUMNNAME>SyncInterval</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.EdgeSettings</FullTableName><COLUMNNAME>WillSyncServerOffline</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>11</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.IpSettings</FullTableName><COLUMNNAME>IpSettingId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.IpSettings</FullTableName><COLUMNNAME>IpAddress</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.IpSettings</FullTableName><COLUMNNAME>UserName</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>40</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.IpSettings</FullTableName><COLUMNNAME>Password</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.IpSettings</FullTableName><COLUMNNAME>Manufacturer</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>80</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.IpSettings</FullTableName><COLUMNNAME>Model</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.IpSettings</FullTableName><COLUMNNAME>ProtocolType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.IpSettings</FullTableName><COLUMNNAME>RttpPort</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.IpSettings</FullTableName><COLUMNNAME>ControlPort</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.IpSettings</FullTableName><COLUMNNAME>StreamPort</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.IpSettings</FullTableName><COLUMNNAME>RtspTcpPort</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>11</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.IpSettings</FullTableName><COLUMNNAME>RtspUdpPort</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>12</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.IpSettings</FullTableName><COLUMNNAME>HttpPort</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>13</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.IpSettings</FullTableName><COLUMNNAME>Path</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>2048</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>14</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.IpSettings</FullTableName><COLUMNNAME>Timeout</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>15</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.IpSettings</FullTableName><COLUMNNAME>Retries</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>16</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.IpSettings</FullTableName><COLUMNNAME>UseAxisProxy</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>17</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.IpSettings</FullTableName><COLUMNNAME>KeepAliveMethodType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>18</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.IpSettings</FullTableName><COLUMNNAME>UseAxisStreamProfile</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>19</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.IpSettings</FullTableName><COLUMNNAME>AxisStreamProfile</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>20</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.MediaStreams</FullTableName><COLUMNNAME>MediaStreamId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.MediaStreams</FullTableName><COLUMNNAME>CameraId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>cam.MediaStreams</FullTableName><COLUMNNAME>StreamType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.MediaStreams</FullTableName><COLUMNNAME>StreamName</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>64</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.MediaStreams</FullTableName><COLUMNNAME>StreamTokenName</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>64</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.MediaStreams</FullTableName><COLUMNNAME>StreamNo</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.MediaStreams</FullTableName><COLUMNNAME>MultiStreamNo</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.MediaStreams</FullTableName><COLUMNNAME>VideoSourceFormatType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.MediaStreams</FullTableName><COLUMNNAME>VideoSourceFormatProfileType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.MediaStreams</FullTableName><COLUMNNAME>VideoWidth</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.MediaStreams</FullTableName><COLUMNNAME>VideoHeight</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>11</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.MediaStreams</FullTableName><COLUMNNAME>VideoResolution</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>12</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.MediaStreams</FullTableName><COLUMNNAME>VideoQuality</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>13</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.MediaStreams</FullTableName><COLUMNNAME>VideoFps</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>14</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.MediaStreams</FullTableName><COLUMNNAME>VideoGov</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>15</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.MediaStreams</FullTableName><COLUMNNAME>VideoBitrateControlType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>16</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.MediaStreams</FullTableName><COLUMNNAME>VideoAverageBitrate</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>17</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.MediaStreams</FullTableName><COLUMNNAME>VideoMaximumBitrate</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>18</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.MediaStreams</FullTableName><COLUMNNAME>IsAudioEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>19</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.MediaStreams</FullTableName><COLUMNNAME>AudioDeviceId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>20</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>cam.MediaStreams</FullTableName><COLUMNNAME>AudioChannelNo</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>21</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.MediaStreams</FullTableName><COLUMNNAME>VideoRecordFormatType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>22</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.MediaStreams</FullTableName><COLUMNNAME>VideoLiveFormatType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>23</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.MediaStreams</FullTableName><COLUMNNAME>IsScheduledRecordingEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>24</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.MediaStreams</FullTableName><COLUMNNAME>IsAlarmRecordingEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>25</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.MediaStreams</FullTableName><COLUMNNAME>IsMotionRecordingEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>26</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.MediaStreams</FullTableName><COLUMNNAME>IsOnDemandCamera</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>27</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.MediaStreams</FullTableName><COLUMNNAME>OnDemandDisconnectTime</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>28</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.MediaStreams</FullTableName><COLUMNNAME>RecompressionOn</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>29</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.MotionZones</FullTableName><COLUMNNAME>MotionZoneId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.MotionZones</FullTableName><COLUMNNAME>CameraId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>cam.MotionZones</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.MotionZones</FullTableName><COLUMNNAME>ZoneIndex</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.MotionZones</FullTableName><COLUMNNAME>Name</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>64</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.MotionZones</FullTableName><COLUMNNAME>PointX</COLUMNNAME><DATATYPE>float</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>53</NUMERICPRECISION><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.MotionZones</FullTableName><COLUMNNAME>PointY</COLUMNNAME><DATATYPE>float</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>53</NUMERICPRECISION><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.MotionZones</FullTableName><COLUMNNAME>Width</COLUMNNAME><DATATYPE>float</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>53</NUMERICPRECISION><NUMERICSCALE /><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.MotionZones</FullTableName><COLUMNNAME>Height</COLUMNNAME><DATATYPE>float</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>53</NUMERICPRECISION><NUMERICSCALE /><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.MotionZones</FullTableName><COLUMNNAME>Priority</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.MotionZones</FullTableName><COLUMNNAME>DoRecSchedule</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>11</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.Presets</FullTableName><COLUMNNAME>PresetId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.Presets</FullTableName><COLUMNNAME>PtzSettingId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>cam.Presets</FullTableName><COLUMNNAME>Name</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>32</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.Presets</FullTableName><COLUMNNAME>PresetIndex</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.Presets</FullTableName><COLUMNNAME>DwellTimeSeconds</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.Presets</FullTableName><COLUMNNAME>DoIncludeTour</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.ProCamps</FullTableName><COLUMNNAME>ProCampId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.ProCamps</FullTableName><COLUMNNAME>MediaStreamId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>cam.ProCamps</FullTableName><COLUMNNAME>PropertyType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.ProCamps</FullTableName><COLUMNNAME>Value</COLUMNNAME><DATATYPE>bigint</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>19</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.ProCamps</FullTableName><COLUMNNAME>Flags</COLUMNNAME><DATATYPE>bigint</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>19</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.PtzSettings</FullTableName><COLUMNNAME>PtzSettingId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.PtzSettings</FullTableName><COLUMNNAME>IsPtzEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.PtzSettings</FullTableName><COLUMNNAME>ChannelNo</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.PtzSettings</FullTableName><COLUMNNAME>PtzDriver</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.PtzSettings</FullTableName><COLUMNNAME>SerialPort</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.PtzSettings</FullTableName><COLUMNNAME>DoFlipDirections</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.PtzSettings</FullTableName><COLUMNNAME>TourResumeTime</COLUMNNAME><DATATYPE>time</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.PtzSettings</FullTableName><COLUMNNAME>InactivityTimeoutInterval</COLUMNNAME><DATATYPE>time</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.PtzSettings</FullTableName><COLUMNNAME>DoAutoHome</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.PtzSettings</FullTableName><COLUMNNAME>DoPresetTour</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.PtzSettings</FullTableName><COLUMNNAME>DoTourOffOnStart</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>11</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.PtzSettings</FullTableName><COLUMNNAME>DoPersistConnection</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>12</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.PtzSettings</FullTableName><COLUMNNAME>UseAnalogPtz</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>13</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.PtzSettings</FullTableName><COLUMNNAME>HomePreset1</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>14</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.PtzSettings</FullTableName><COLUMNNAME>HomePreset2</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>15</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.PtzSettings</FullTableName><COLUMNNAME>HomePreset3</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>16</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.PtzSettings</FullTableName><COLUMNNAME>HomePreset4</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>17</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.RecordingSettings</FullTableName><COLUMNNAME>RecordingSettingId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.RecordingSettings</FullTableName><COLUMNNAME>StorageType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.RecordingSettings</FullTableName><COLUMNNAME>SchedFps</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.RecordingSettings</FullTableName><COLUMNNAME>MotionFps</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.RecordingSettings</FullTableName><COLUMNNAME>AlarmFps</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.RecordingSettings</FullTableName><COLUMNNAME>PreMotionSeconds</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.RecordingSettings</FullTableName><COLUMNNAME>PostMotionSeconds</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.RecordingSettings</FullTableName><COLUMNNAME>PreAlarmSecords</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.RecordingSettings</FullTableName><COLUMNNAME>PostAlarmSeconds</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.RetentionPolicies</FullTableName><COLUMNNAME>RetentionPolicyId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.RetentionPolicies</FullTableName><COLUMNNAME>CameraId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>cam.RetentionPolicies</FullTableName><COLUMNNAME>PoolType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.RetentionPolicies</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.RetentionPolicies</FullTableName><COLUMNNAME>ActionAfterDays</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.RetentionPolicies</FullTableName><COLUMNNAME>MinRetentionDays</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.RetentionPolicies</FullTableName><COLUMNNAME>MaxRetentionDays</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.RetentionPolicies</FullTableName><COLUMNNAME>StartTime</COLUMNNAME><DATATYPE>time</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.RetentionPolicies</FullTableName><COLUMNNAME>ContinuousClips</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.RetentionPolicies</FullTableName><COLUMNNAME>MotionClips</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.RetentionPolicies</FullTableName><COLUMNNAME>AlarmClips</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>11</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.StreamProcessings</FullTableName><COLUMNNAME>StreamProcessingId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.StreamProcessings</FullTableName><COLUMNNAME>ExternalCameraNo</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.StreamProcessings</FullTableName><COLUMNNAME>IsStreamProcessingEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.StreamProcessings</FullTableName><COLUMNNAME>IsDftEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.StreamProcessings</FullTableName><COLUMNNAME>IsDvdEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.StreamProcessings</FullTableName><COLUMNNAME>WillFavorContinuousVideo</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.StreamProcessings</FullTableName><COLUMNNAME>DoVideoAnalytics</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.StreamProcessings</FullTableName><COLUMNNAME>LatencyDft</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.StreamProcessings</FullTableName><COLUMNNAME>GopCountDft</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.StretchSettings</FullTableName><COLUMNNAME>StretchSettingId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.StretchSettings</FullTableName><COLUMNNAME>IsDeinterlacingEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.StretchSettings</FullTableName><COLUMNNAME>IsMotionBasedDeinterlacingEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.StretchSettings</FullTableName><COLUMNNAME>MotionDeinterlacingThreshold</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.StretchSettings</FullTableName><COLUMNNAME>IsMedianFilteringEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.StretchSettings</FullTableName><COLUMNNAME>IsNoiseReductionEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.StretchSettings</FullTableName><COLUMNNAME>NoiseReductionLumaStrength</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.StretchSettings</FullTableName><COLUMNNAME>NoiseReductionChromaStrength</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.VideoOverlays</FullTableName><COLUMNNAME>VideoOverlayId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.VideoOverlays</FullTableName><COLUMNNAME>StreamProcessingId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>cam.VideoOverlays</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.VideoOverlays</FullTableName><COLUMNNAME>Name</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>64</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.VideoOverlays</FullTableName><COLUMNNAME>VideoOverlayType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cam.VideoOverlays</FullTableName><COLUMNNAME>VideoOverlayPositionType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cfg.RegionMembers</FullTableName><COLUMNNAME>RegionMemberId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cfg.RegionMembers</FullTableName><COLUMNNAME>RegionHierarchyId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>cfg.RegionMembers</FullTableName><COLUMNNAME>RecordingServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>cfg.RegionMembers</FullTableName><COLUMNNAME>ViewId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>cfg.RegionsHierarchy</FullTableName><COLUMNNAME>RegionHierarchyId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cfg.RegionsHierarchy</FullTableName><COLUMNNAME>RegionGuid</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cfg.RegionsHierarchy</FullTableName><COLUMNNAME>ParentId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>cfg.RegionsHierarchy</FullTableName><COLUMNNAME>RegionTypeId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>cfg.RegionsHierarchy</FullTableName><COLUMNNAME>Name</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>500</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cfg.RegionTypes</FullTableName><COLUMNNAME>RegionTypeId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cfg.RegionTypes</FullTableName><COLUMNNAME>Ordinal</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cfg.RegionTypes</FullTableName><COLUMNNAME>Name</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>64</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cld.Credentials</FullTableName><COLUMNNAME>Email</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cld.Credentials</FullTableName><COLUMNNAME>Password</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cld.Credentials</FullTableName><COLUMNNAME>Secret</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cld.Service</FullTableName><COLUMNNAME>HostAddress</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cld.Service</FullTableName><COLUMNNAME>Port</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cld.Service</FullTableName><COLUMNNAME>IsSecure</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cld.Service</FullTableName><COLUMNNAME>DefaultTimeout</COLUMNNAME><DATATYPE>time</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cld.VpnProfilePush</FullTableName><COLUMNNAME>VpnProfilePushId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cld.VpnProfilePush</FullTableName><COLUMNNAME>ServerGuid</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cld.VpnProfilePush</FullTableName><COLUMNNAME>VpnAddress</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.ActiveDirectories</FullTableName><COLUMNNAME>ActiveDirectoryId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.ActiveDirectories</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.ActiveDirectories</FullTableName><COLUMNNAME>Domain</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.ActiveDirectories</FullTableName><COLUMNNAME>BaseDn</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.ActiveDirectories</FullTableName><COLUMNNAME>Username</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>40</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.ActiveDirectories</FullTableName><COLUMNNAME>Password</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.ActiveDirectories</FullTableName><COLUMNNAME>SearchNestedDomains</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.ActiveDirectories</FullTableName><COLUMNNAME>SearchNestedGroups</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.ActiveDirectories</FullTableName><COLUMNNAME>GroupReauthIntervalSecs</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.Certificates</FullTableName><COLUMNNAME>CertificateId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.Certificates</FullTableName><COLUMNNAME>LocalSecureWebServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>com.Certificates</FullTableName><COLUMNNAME>Name</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.Connections</FullTableName><COLUMNNAME>ConnectionId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.Connections</FullTableName><COLUMNNAME>HostAddress</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.Connections</FullTableName><COLUMNNAME>Username</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>40</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.Connections</FullTableName><COLUMNNAME>Password</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.Connections</FullTableName><COLUMNNAME>Driver</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.Connections</FullTableName><COLUMNNAME>Retries</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.EmailServers</FullTableName><COLUMNNAME>EmailServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.EmailServers</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.EmailServers</FullTableName><COLUMNNAME>SmtpHostAddress</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.EmailServers</FullTableName><COLUMNNAME>SmtpHostPort</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.EmailServers</FullTableName><COLUMNNAME>Username</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>40</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.EmailServers</FullTableName><COLUMNNAME>Password</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.EmailServers</FullTableName><COLUMNNAME>WillUseTls</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.EmailServers</FullTableName><COLUMNNAME>WillAuthenticate</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.EmailServers</FullTableName><COLUMNNAME>SenderName</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>60</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.EmailServers</FullTableName><COLUMNNAME>SenderEmail</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.LocalSecureWebServers</FullTableName><COLUMNNAME>LocalSecureWebServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.LocalSecureWebServers</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.LocalSecureWebServers</FullTableName><COLUMNNAME>Port</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.LocalSecureWebServers</FullTableName><COLUMNNAME>MinTlsVersion</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.LocalWebServers</FullTableName><COLUMNNAME>LocalWebServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.LocalWebServers</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.LocalWebServers</FullTableName><COLUMNNAME>Port</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.Ports</FullTableName><COLUMNNAME>PortId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.Ports</FullTableName><COLUMNNAME>DataPort</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.Ports</FullTableName><COLUMNNAME>SecureDataPort</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.Ports</FullTableName><COLUMNNAME>AdminPort</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.Ports</FullTableName><COLUMNNAME>UdpMediaPortMin</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.Ports</FullTableName><COLUMNNAME>UdpMediaPortMax</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.Ports</FullTableName><COLUMNNAME>RtspPortIsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.Ports</FullTableName><COLUMNNAME>RtspPort</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>com.Ports</FullTableName><COLUMNNAME>RtspUseBasicAuth</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT>((0))</COLUMNDEFAULT><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cs.CommonSettings</FullTableName><COLUMNNAME>CommonSettingId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cs.CommonSettings</FullTableName><COLUMNNAME>Settings</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>-1</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cs.LoggingComponents</FullTableName><COLUMNNAME>LoggingComponentType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cs.LoggingComponents</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cs.LoggingComponents</FullTableName><COLUMNNAME>Identifier</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>64</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cs.LoggingComponents</FullTableName><COLUMNNAME>Name</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cso.BandwidthControls</FullTableName><COLUMNNAME>BandwidthControlId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cso.BandwidthControls</FullTableName><COLUMNNAME>RecordingServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>cso.BandwidthControls</FullTableName><COLUMNNAME>IsOnDemandEventsChecked</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>cso.BandwidthControls</FullTableName><COLUMNNAME>StatusPeriodMs</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>dbo.Migrations</FullTableName><COLUMNNAME>MigrationId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>dbo.Migrations</FullTableName><COLUMNNAME>SQL</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>4000</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>dbo.Migrations</FullTableName><COLUMNNAME>FileName</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>255</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>dbo.Migrations</FullTableName><COLUMNNAME>AppliedDate</COLUMNNAME><DATATYPE>datetime</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT>(getdate())</COLUMNDEFAULT><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>dbo.Migrations</FullTableName><COLUMNNAME>SqlHash</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>grp.GroupMembers</FullTableName><COLUMNNAME>GroupMemberId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>grp.GroupMembers</FullTableName><COLUMNNAME>GroupId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>grp.GroupMembers</FullTableName><COLUMNNAME>UserId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>grp.Groups</FullTableName><COLUMNNAME>GroupId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>grp.Groups</FullTableName><COLUMNNAME>ConfigurationId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>grp.Groups</FullTableName><COLUMNNAME>ParentId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>grp.Groups</FullTableName><COLUMNNAME>ActiveDirectoryId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>grp.Groups</FullTableName><COLUMNNAME>SubDomain</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>grp.Groups</FullTableName><COLUMNNAME>GroupGuid</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>grp.Groups</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>grp.Groups</FullTableName><COLUMNNAME>IsAdmin</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>grp.Groups</FullTableName><COLUMNNAME>RemoteAccess</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>grp.Groups</FullTableName><COLUMNNAME>Name</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>500</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>grp.Groups</FullTableName><COLUMNNAME>PtzPriority</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>11</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>grp.Groups</FullTableName><COLUMNNAME>Note</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>12</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>grp.Groups</FullTableName><COLUMNNAME>MsOwnerGuid</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>13</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>lic.Licenses</FullTableName><COLUMNNAME>LicenseId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>lic.Licenses</FullTableName><COLUMNNAME>LicenseType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>lic.Licenses</FullTableName><COLUMNNAME>IpLicensesPurchased</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>lic.Licenses</FullTableName><COLUMNNAME>AnalogLicensesPurchased</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>lic.Licenses</FullTableName><COLUMNNAME>FeatureKeysJson</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>4000</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.BackupConfigs</FullTableName><COLUMNNAME>BackupConfigId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.BackupConfigs</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.BackupConfigs</FullTableName><COLUMNNAME>IsDefault</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.BackupConfigs</FullTableName><COLUMNNAME>Name</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>64</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.BackupConfigs</FullTableName><COLUMNNAME>CronExpression</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.BackupConfigs</FullTableName><COLUMNNAME>BackupPath</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.BackupConfigs</FullTableName><COLUMNNAME>BackupTimestampFormat</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>32</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.BackupConfigs</FullTableName><COLUMNNAME>BackupOptions</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>-1</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.DicoveredServers</FullTableName><COLUMNNAME>DicoveredServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.DicoveredServers</FullTableName><COLUMNNAME>IpAddress</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.DicoveredServers</FullTableName><COLUMNNAME>FriendlyName</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>80</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.DicoveredServers</FullTableName><COLUMNNAME>ServerGuid</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.DicoveredServers</FullTableName><COLUMNNAME>LastUpdated</COLUMNNAME><DATATYPE>datetime</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.FederatedChildren</FullTableName><COLUMNNAME>Guid</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.FederatedChildren</FullTableName><COLUMNNAME>Name</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>100</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.FederatedChildren</FullTableName><COLUMNNAME>Version</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>64</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.FederatedChildren</FullTableName><COLUMNNAME>UseTls</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.FederatedChildren</FullTableName><COLUMNNAME>HostAddress</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.FederatedChildren</FullTableName><COLUMNNAME>Port</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.FederatedChildren</FullTableName><COLUMNNAME>Identity</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.FederatedChildren</FullTableName><COLUMNNAME>Password</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.FederatedChildren</FullTableName><COLUMNNAME>Note</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.FederatedChildren</FullTableName><COLUMNNAME>WriteToken</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.FederatedChildren</FullTableName><COLUMNNAME>RecordingServersCount</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT>((0))</COLUMNDEFAULT><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>11</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.FederatedChildren</FullTableName><COLUMNNAME>CameraLicensesPurchased</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT>((0))</COLUMNDEFAULT><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>12</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.FederatedChildren</FullTableName><COLUMNNAME>CameraLicensesUsed</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT>((0))</COLUMNDEFAULT><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>13</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.FederatedParent</FullTableName><COLUMNNAME>Guid</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.FederatedParent</FullTableName><COLUMNNAME>Name</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>100</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.FederatedParent</FullTableName><COLUMNNAME>Version</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>64</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.FederatedParent</FullTableName><COLUMNNAME>UseTls</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.FederatedParent</FullTableName><COLUMNNAME>HostAddress</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.FederatedParent</FullTableName><COLUMNNAME>Port</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.FederatedParent</FullTableName><COLUMNNAME>Note</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.FederatedParent</FullTableName><COLUMNNAME>WriteToken</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.FederatedSiblings</FullTableName><COLUMNNAME>Guid</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.FederatedSiblings</FullTableName><COLUMNNAME>Name</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>100</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.FederatedSiblings</FullTableName><COLUMNNAME>Version</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>64</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.FederatedSiblings</FullTableName><COLUMNNAME>UseTls</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.FederatedSiblings</FullTableName><COLUMNNAME>HostAddress</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.FederatedSiblings</FullTableName><COLUMNNAME>Port</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.FederationSettings</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.Identity</FullTableName><COLUMNNAME>Guid</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.Identity</FullTableName><COLUMNNAME>Name</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>100</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.Identity</FullTableName><COLUMNNAME>HostAddressesJson</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>2000</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.Identity</FullTableName><COLUMNNAME>BaseUrl</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportRecordingServerQueue</FullTableName><COLUMNNAME>ImportRecordingServerQueueId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportRecordingServerQueue</FullTableName><COLUMNNAME>FriendlyName</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>100</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportRecordingServerQueue</FullTableName><COLUMNNAME>HostAddress</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>100</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportRecordingServerQueue</FullTableName><COLUMNNAME>SvrCtrlPortNo</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportRecordingServerQueue</FullTableName><COLUMNNAME>RestPortNo</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportRecordingServerQueue</FullTableName><COLUMNNAME>UseTls</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportRecordingServerQueue</FullTableName><COLUMNNAME>Username</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>64</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportRecordingServerQueue</FullTableName><COLUMNNAME>Password</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportRecordingServerQueue</FullTableName><COLUMNNAME>AutoProvision</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportRecordingServerQueue</FullTableName><COLUMNNAME>ConnectionTimeoutMs</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportRecordingServerQueue</FullTableName><COLUMNNAME>AddUserId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>11</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportRecordingServerQueue</FullTableName><COLUMNNAME>EnqueuedAt</COLUMNNAME><DATATYPE>datetime</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>12</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportRecordingServerQueue</FullTableName><COLUMNNAME>RegionHierarchyId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>13</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportRecordingServerQueue</FullTableName><COLUMNNAME>PostActions</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>4000</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>14</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportRecordingServerStatus</FullTableName><COLUMNNAME>ImportRecordingServerStatusId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportRecordingServerStatus</FullTableName><COLUMNNAME>FriendlyName</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>100</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportRecordingServerStatus</FullTableName><COLUMNNAME>HostAddress</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>100</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportRecordingServerStatus</FullTableName><COLUMNNAME>SvrCtrlPortNo</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportRecordingServerStatus</FullTableName><COLUMNNAME>RestPortNo</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportRecordingServerStatus</FullTableName><COLUMNNAME>UseTls</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportRecordingServerStatus</FullTableName><COLUMNNAME>Username</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>64</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportRecordingServerStatus</FullTableName><COLUMNNAME>Password</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportRecordingServerStatus</FullTableName><COLUMNNAME>AutoProvision</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportRecordingServerStatus</FullTableName><COLUMNNAME>AddUserId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportRecordingServerStatus</FullTableName><COLUMNNAME>RegionHierarchyId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>11</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportRecordingServerStatus</FullTableName><COLUMNNAME>EnqueuedAt</COLUMNNAME><DATATYPE>datetime</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>12</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportRecordingServerStatus</FullTableName><COLUMNNAME>ImportStatus</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>13</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportRecordingServerStatus</FullTableName><COLUMNNAME>StartedAt</COLUMNNAME><DATATYPE>datetime</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>14</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportRecordingServerStatus</FullTableName><COLUMNNAME>EndedAt</COLUMNNAME><DATATYPE>datetime</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>15</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportRecordingServerStatus</FullTableName><COLUMNNAME>Elapsed</COLUMNNAME><DATATYPE>time</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>16</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportRecordingServerStatus</FullTableName><COLUMNNAME>ErrorMessage</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>17</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportRecordingServerStatus</FullTableName><COLUMNNAME>PostActions</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>4000</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>18</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportServerConfigQueue</FullTableName><COLUMNNAME>ImportServerConfigQueueGuid</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportServerConfigQueue</FullTableName><COLUMNNAME>AutoProvision</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportServerConfigQueue</FullTableName><COLUMNNAME>FriendlyName</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>100</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportServerConfigQueue</FullTableName><COLUMNNAME>HostAddress</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>100</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportServerConfigQueue</FullTableName><COLUMNNAME>ServerGuid</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportServerConfigQueue</FullTableName><COLUMNNAME>Version</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>16</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportServerConfigQueue</FullTableName><COLUMNNAME>UseTls</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportServerConfigQueue</FullTableName><COLUMNNAME>Username</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>64</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportServerConfigQueue</FullTableName><COLUMNNAME>Password</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportServerConfigQueue</FullTableName><COLUMNNAME>Platform</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>64</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportServerConfigQueue</FullTableName><COLUMNNAME>SerialNo</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>64</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>11</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportServerConfigQueue</FullTableName><COLUMNNAME>EnqueuedAt</COLUMNNAME><DATATYPE>datetime</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>12</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportServerConfigQueue</FullTableName><COLUMNNAME>ServerTimeZoneInfo</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>100</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>13</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportServerConfigQueue</FullTableName><COLUMNNAME>EnvironmentInfo</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>-1</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>14</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportServerConfigQueue</FullTableName><COLUMNNAME>ServerConfigRoot</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>-1</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>15</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportServerConfigQueue</FullTableName><COLUMNNAME>LicenseInfo</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>400</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>16</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportServerConfigStatus</FullTableName><COLUMNNAME>ImportServerConfigStatusGuid</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportServerConfigStatus</FullTableName><COLUMNNAME>HostAddress</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>100</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportServerConfigStatus</FullTableName><COLUMNNAME>ServerGuid</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportServerConfigStatus</FullTableName><COLUMNNAME>StartedAt</COLUMNNAME><DATATYPE>datetime</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportServerConfigStatus</FullTableName><COLUMNNAME>EndedAt</COLUMNNAME><DATATYPE>datetime</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportServerConfigStatus</FullTableName><COLUMNNAME>Elapsed</COLUMNNAME><DATATYPE>time</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportServerConfigStatus</FullTableName><COLUMNNAME>WasSuccessful</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.ImportServerConfigStatus</FullTableName><COLUMNNAME>Message</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>1024</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.NamedResources</FullTableName><COLUMNNAME>NamedResourceId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.NamedResources</FullTableName><COLUMNNAME>ResourceType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.NamedResources</FullTableName><COLUMNNAME>Name</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>64</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.NamedResources</FullTableName><COLUMNNAME>Description</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.NamedResources</FullTableName><COLUMNNAME>HostAddress</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.NamedResources</FullTableName><COLUMNNAME>SecondaryAddress</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.PushServerStatus</FullTableName><COLUMNNAME>PushServerStatusId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.PushServerStatus</FullTableName><COLUMNNAME>RecordingServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>ms.PushServerStatus</FullTableName><COLUMNNAME>PushToServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.PushServerStatus</FullTableName><COLUMNNAME>PushDevices</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.PushServerStatus</FullTableName><COLUMNNAME>PushUsers</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.PushServerStatus</FullTableName><COLUMNNAME>PushConfiguration</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.PushServerStatus</FullTableName><COLUMNNAME>CorsWhitelist</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>-1</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.PushServerStatus</FullTableName><COLUMNNAME>TimeStamp</COLUMNNAME><DATATYPE>datetime</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.PushServerStatus</FullTableName><COLUMNNAME>WasSuccessful</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.PushServerStatus</FullTableName><COLUMNNAME>ErrorMessage</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.PushServerStatus</FullTableName><COLUMNNAME>PushMediaEnc</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT>((0))</COLUMNDEFAULT><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>11</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.PushToServers</FullTableName><COLUMNNAME>PushToServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.PushToServers</FullTableName><COLUMNNAME>RecordingServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>ms.PushToServers</FullTableName><COLUMNNAME>PushDevices</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.PushToServers</FullTableName><COLUMNNAME>PushUsers</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.PushToServers</FullTableName><COLUMNNAME>PushConfiguration</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.PushToServers</FullTableName><COLUMNNAME>CorsWhitelist</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>-1</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.PushToServers</FullTableName><COLUMNNAME>TimeStamp</COLUMNNAME><DATATYPE>datetime</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.PushToServers</FullTableName><COLUMNNAME>HttpPort</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.PushToServers</FullTableName><COLUMNNAME>SecureHttpPort</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.PushToServers</FullTableName><COLUMNNAME>Priority</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT>((0))</COLUMNDEFAULT><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.PushToServers</FullTableName><COLUMNNAME>PushMediaEnc</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT>((0))</COLUMNDEFAULT><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>11</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.VersionInfo</FullTableName><COLUMNNAME>VersionInfoId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.VersionInfo</FullTableName><COLUMNNAME>Major</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.VersionInfo</FullTableName><COLUMNNAME>Minor</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.VersionInfo</FullTableName><COLUMNNAME>Build</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.VersionInfo</FullTableName><COLUMNNAME>Revision</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>ms.VersionInfo</FullTableName><COLUMNNAME>TimeStamp</COLUMNNAME><DATATYPE>datetime</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT>(NULL)</COLUMNDEFAULT><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.DefaultBehaviors</FullTableName><COLUMNNAME>DefaultBehaviorId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.DefaultBehaviors</FullTableName><COLUMNNAME>RecordingServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.DefaultBehaviors</FullTableName><COLUMNNAME>PacketLossMaxAcceptPercent</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.DefaultBehaviors</FullTableName><COLUMNNAME>PacketLossReportIntervalSec</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.DefaultBehaviors</FullTableName><COLUMNNAME>IsCameraAccessEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventNotifications</FullTableName><COLUMNNAME>EventNotificationId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventNotifications</FullTableName><COLUMNNAME>EmailSubject</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventNotifications</FullTableName><COLUMNNAME>EmailRecipients</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>-1</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventNotifications</FullTableName><COLUMNNAME>SmsSubject</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventNotifications</FullTableName><COLUMNNAME>SmsRecipients</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>-1</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventNotifications</FullTableName><COLUMNNAME>SmsGateway</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventNotificationSettings</FullTableName><COLUMNNAME>EventNotificationSettingId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventNotificationSettings</FullTableName><COLUMNNAME>EventNotificationId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventNotificationSettings</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventNotificationSettings</FullTableName><COLUMNNAME>SourceType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventNotificationSettings</FullTableName><COLUMNNAME>EventType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventNotificationSettings</FullTableName><COLUMNNAME>WillIncludeSnapshot</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventNotificationSettings</FullTableName><COLUMNNAME>MinInterval</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventNotificationSettings</FullTableName><COLUMNNAME>ConfirmationWaitFor</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventNotificationSettings</FullTableName><COLUMNNAME>ConfirmationWaitEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventsToLog</FullTableName><COLUMNNAME>EventToLogId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventsToLog</FullTableName><COLUMNNAME>RecordingServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventsToLog</FullTableName><COLUMNNAME>EventType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventTriggerActions</FullTableName><COLUMNNAME>EventTriggerActionId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventTriggerActions</FullTableName><COLUMNNAME>EventTriggerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventTriggerActions</FullTableName><COLUMNNAME>EventTriggerActionGuid</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventTriggerActions</FullTableName><COLUMNNAME>ActionType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventTriggerActions</FullTableName><COLUMNNAME>ActionValue</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventTriggerActions</FullTableName><COLUMNNAME>PriorityType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventTriggerActions</FullTableName><COLUMNNAME>IsPrimaryAction</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventTriggerActions</FullTableName><COLUMNNAME>CameraId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventTriggerActions</FullTableName><COLUMNNAME>IOTriggerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventTriggerActions</FullTableName><COLUMNNAME>VolumeId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventTriggerResponseActions</FullTableName><COLUMNNAME>EventTriggerResponseActionId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventTriggerResponseActions</FullTableName><COLUMNNAME>EventTriggerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventTriggerResponseActions</FullTableName><COLUMNNAME>EventTriggerResponseId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventTriggerResponseActions</FullTableName><COLUMNNAME>EventTriggerActionId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventTriggerResponses</FullTableName><COLUMNNAME>EventTriggerResponseId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventTriggerResponses</FullTableName><COLUMNNAME>EventTriggerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventTriggerResponses</FullTableName><COLUMNNAME>IsAckRequired</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventTriggerResponses</FullTableName><COLUMNNAME>IsResetRequired</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventTriggerResponses</FullTableName><COLUMNNAME>CanForward</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventTriggers</FullTableName><COLUMNNAME>EventTriggerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventTriggers</FullTableName><COLUMNNAME>RecordingServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventTriggers</FullTableName><COLUMNNAME>EventTriggerResponseId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventTriggers</FullTableName><COLUMNNAME>EventTriggerGuid</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventTriggers</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventTriggers</FullTableName><COLUMNNAME>Name</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventTriggers</FullTableName><COLUMNNAME>SetIOTrigger</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventTriggers</FullTableName><COLUMNNAME>PriorityType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventTriggers</FullTableName><COLUMNNAME>Note</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventTriggerSources</FullTableName><COLUMNNAME>EventTriggerSourceId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventTriggerSources</FullTableName><COLUMNNAME>EventTriggerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventTriggerSources</FullTableName><COLUMNNAME>EventSourceGuid</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventTriggerSources</FullTableName><COLUMNNAME>EventType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventTriggerSources</FullTableName><COLUMNNAME>EventValue</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventTriggerSources</FullTableName><COLUMNNAME>IsPrimaryEvent</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventTriggerSources</FullTableName><COLUMNNAME>CameraId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventTriggerSources</FullTableName><COLUMNNAME>IOTriggerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventTriggerSources</FullTableName><COLUMNNAME>VolumeId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.EventTriggerSources</FullTableName><COLUMNNAME>UserId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.IoInputs</FullTableName><COLUMNNAME>IoInputId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.IoInputs</FullTableName><COLUMNNAME>IOTriggerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.IoInputs</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.IoInputs</FullTableName><COLUMNNAME>Name</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>80</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.IoInputs</FullTableName><COLUMNNAME>PinNo</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.IoInputs</FullTableName><COLUMNNAME>TriggerStateType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.IoInputs</FullTableName><COLUMNNAME>EventType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.IoOutputCameras</FullTableName><COLUMNNAME>IoOutputCameraId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.IoOutputCameras</FullTableName><COLUMNNAME>IoTriggerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.IoOutputCameras</FullTableName><COLUMNNAME>IoOutputId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.IoOutputCameras</FullTableName><COLUMNNAME>CameraId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.IoOutputs</FullTableName><COLUMNNAME>IoOutputId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.IoOutputs</FullTableName><COLUMNNAME>IOTriggerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.IoOutputs</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.IoOutputs</FullTableName><COLUMNNAME>Name</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>80</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.IoOutputs</FullTableName><COLUMNNAME>PinNo</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.IoOutputs</FullTableName><COLUMNNAME>TriggerStateType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.IoOutputs</FullTableName><COLUMNNAME>ResetTimeMs</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.IoOutputs</FullTableName><COLUMNNAME>DeviceType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.IoOutputs</FullTableName><COLUMNNAME>WillAutoReset</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.IOTriggers</FullTableName><COLUMNNAME>IOTriggerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.IOTriggers</FullTableName><COLUMNNAME>RecordingServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.IOTriggers</FullTableName><COLUMNNAME>IODeviceId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.IOTriggers</FullTableName><COLUMNNAME>IOTriggerGuid</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.IOTriggers</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.IOTriggers</FullTableName><COLUMNNAME>Name</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>64</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.IOTriggers</FullTableName><COLUMNNAME>Username</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>64</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.IOTriggers</FullTableName><COLUMNNAME>Password</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.IOTriggers</FullTableName><COLUMNNAME>DriverName</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.IOTriggers</FullTableName><COLUMNNAME>IpAddress</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.IOTriggers</FullTableName><COLUMNNAME>Note</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>11</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.Loggers</FullTableName><COLUMNNAME>LoggerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.Loggers</FullTableName><COLUMNNAME>LogSettingId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.Loggers</FullTableName><COLUMNNAME>ComponentNo</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.Loggers</FullTableName><COLUMNNAME>PriorityNo</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.Loggers</FullTableName><COLUMNNAME>DoFlushOnWrite</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.Loggers</FullTableName><COLUMNNAME>DoRolloverOnSaveCfg</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.Loggers</FullTableName><COLUMNNAME>KeepForDays</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.LogSettings</FullTableName><COLUMNNAME>LogSettingId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.LogSettings</FullTableName><COLUMNNAME>LogFilePath</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>512</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.MediaEncryptions</FullTableName><COLUMNNAME>ServerGuid</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.MediaEncryptions</FullTableName><COLUMNNAME>Enabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.MediaEncryptions</FullTableName><COLUMNNAME>CipherMode</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.MediaEncryptions</FullTableName><COLUMNNAME>KeySize</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.MediaEncryptions</FullTableName><COLUMNNAME>PasswordBase64</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>1024</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.MediaEncryptions</FullTableName><COLUMNNAME>Iterations</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.MediaEncryptions</FullTableName><COLUMNNAME>HashAlgorithm</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.MediaEncryptions</FullTableName><COLUMNNAME>KekBase64</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>512</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.MediaEncryptions</FullTableName><COLUMNNAME>DekBase64</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>512</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.MediaEncryptions</FullTableName><COLUMNNAME>DekCreatedOnUtc</COLUMNNAME><DATATYPE>datetime</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.MediaEncryptions</FullTableName><COLUMNNAME>Note</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>11</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.MediaEncryptions</FullTableName><COLUMNNAME>WriteToken</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>12</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.NVRCameras</FullTableName><COLUMNNAME>NVRCameraId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.NVRCameras</FullTableName><COLUMNNAME>NVRId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.NVRCameras</FullTableName><COLUMNNAME>CameraId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.NVRCameras</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.NVRCameras</FullTableName><COLUMNNAME>ChannelNo</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.NVRCameras</FullTableName><COLUMNNAME>IsVmsVolume</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.NVRCameras</FullTableName><COLUMNNAME>IsNvrStorage</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.NVRs</FullTableName><COLUMNNAME>NVRId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.NVRs</FullTableName><COLUMNNAME>RecordingServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.NVRs</FullTableName><COLUMNNAME>VideoDeviceId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.NVRs</FullTableName><COLUMNNAME>NVRGuid</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.NVRs</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.NVRs</FullTableName><COLUMNNAME>Name</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>64</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.NVRs</FullTableName><COLUMNNAME>Manufacturer</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>80</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.NVRs</FullTableName><COLUMNNAME>Model</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.NVRs</FullTableName><COLUMNNAME>ChannelCount</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.NVRs</FullTableName><COLUMNNAME>TimeZone</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>64</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.NVRs</FullTableName><COLUMNNAME>HttpPort</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>11</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.NVRs</FullTableName><COLUMNNAME>RtspPort</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>12</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.NVRs</FullTableName><COLUMNNAME>ControlPort</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>13</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.NVRs</FullTableName><COLUMNNAME>HostAddress</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>14</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.NVRs</FullTableName><COLUMNNAME>Username</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>64</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>15</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.NVRs</FullTableName><COLUMNNAME>Password</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>16</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.NVRs</FullTableName><COLUMNNAME>TimeoutSecs</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>17</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.NVRs</FullTableName><COLUMNNAME>NumRetries</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>18</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.NVRs</FullTableName><COLUMNNAME>Note</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>19</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.PresetZones</FullTableName><COLUMNNAME>PresetZoneId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.PresetZones</FullTableName><COLUMNNAME>RecordingServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.PresetZones</FullTableName><COLUMNNAME>PriorityType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.PresetZones</FullTableName><COLUMNNAME>HoldTimeSeconds</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.PresetZones</FullTableName><COLUMNNAME>DwellTimeSeconds</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.PresetZones</FullTableName><COLUMNNAME>IsCyclingEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.RecordingServers</FullTableName><COLUMNNAME>RecordingServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.RecordingServers</FullTableName><COLUMNNAME>RecordingServerGuid</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.RecordingServers</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.RecordingServers</FullTableName><COLUMNNAME>Name</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>250</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.RecordingServers</FullTableName><COLUMNNAME>Description</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.RecordingServers</FullTableName><COLUMNNAME>TimeZone</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>40</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.RecordingServers</FullTableName><COLUMNNAME>Version</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>40</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.RecordingServers</FullTableName><COLUMNNAME>ProductId</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>100</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.RecordingServers</FullTableName><COLUMNNAME>UseTls</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.RecordingServers</FullTableName><COLUMNNAME>IpLicensesUsed</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.RecordingServers</FullTableName><COLUMNNAME>AnalogLicensesUsed</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>11</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.RecordingServers</FullTableName><COLUMNNAME>IsLegacy</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>12</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.RecordingServers</FullTableName><COLUMNNAME>Platform</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>64</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>13</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.RecordingServers</FullTableName><COLUMNNAME>SerialNumber</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>64</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>14</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.RecordingServers</FullTableName><COLUMNNAME>WasAutoProvisioned</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>15</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.RecordingServers</FullTableName><COLUMNNAME>LastSync</COLUMNNAME><DATATYPE>datetime</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>16</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.RecordingServers</FullTableName><COLUMNNAME>FeatureKeyVersion</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>17</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.RecordingServers</FullTableName><COLUMNNAME>Latitude</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>20</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>18</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.RecordingServers</FullTableName><COLUMNNAME>Longitude</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>20</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>19</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.RecordingServers</FullTableName><COLUMNNAME>PortId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>20</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.RecordingServers</FullTableName><COLUMNNAME>ConnectionId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>21</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.RecordingServers</FullTableName><COLUMNNAME>LicenseId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>22</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.RecordingServers</FullTableName><COLUMNNAME>LocalWebServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>23</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.RecordingServers</FullTableName><COLUMNNAME>LocalSecureWebServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>24</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.RecordingServers</FullTableName><COLUMNNAME>LogSettingId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>25</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.RecordingServers</FullTableName><COLUMNNAME>EventNotificationId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>26</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.RecordingServers</FullTableName><COLUMNNAME>UdpBroadcastId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>27</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.RecordingServers</FullTableName><COLUMNNAME>PrimaryRecordingServerFailoverConfigId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>28</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.RecordingServers</FullTableName><COLUMNNAME>StandbyRecordingServerFailoverConfigId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>29</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.RecordingServers</FullTableName><COLUMNNAME>Note</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>30</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.UdpBroadcasts</FullTableName><COLUMNNAME>UdpBroadcastId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.UdpBroadcasts</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.UdpBroadcasts</FullTableName><COLUMNNAME>Port</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.UdpBroadcasts</FullTableName><COLUMNNAME>RepeatCount</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.UdpBroadcasts</FullTableName><COLUMNNAME>WaitTimeoutMs</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VideoDecoders</FullTableName><COLUMNNAME>VideoDecoderId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VideoDecoders</FullTableName><COLUMNNAME>RecordingServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.VideoDecoders</FullTableName><COLUMNNAME>VideoCompressionType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VideoDecoders</FullTableName><COLUMNNAME>TranscoderEngineType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VideoDecoders</FullTableName><COLUMNNAME>VideoCompressionProfileType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VideoDecoders</FullTableName><COLUMNNAME>LatencyType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VideoDecoders</FullTableName><COLUMNNAME>VideoCompressionRateType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VideoDecoders</FullTableName><COLUMNNAME>BitRate</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VideoDecoders</FullTableName><COLUMNNAME>GopLength</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VideoDecoders</FullTableName><COLUMNNAME>HardwareAccelerationType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VideoEncoders</FullTableName><COLUMNNAME>VideoEncoderId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VideoEncoders</FullTableName><COLUMNNAME>RecordingServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.VideoEncoders</FullTableName><COLUMNNAME>VideoCompressionType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VideoEncoders</FullTableName><COLUMNNAME>TranscoderEngineType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VideoEncoders</FullTableName><COLUMNNAME>VideoCompressionProfileType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VideoEncoders</FullTableName><COLUMNNAME>LatencyType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VideoEncoders</FullTableName><COLUMNNAME>VideoCompressionRateType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VideoEncoders</FullTableName><COLUMNNAME>BitRate</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VideoEncoders</FullTableName><COLUMNNAME>GopLength</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VideoEncoders</FullTableName><COLUMNNAME>HardwareAccelerationType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VolumeExpirations</FullTableName><COLUMNNAME>VolumeExpirationId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VolumeExpirations</FullTableName><COLUMNNAME>VolumeId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.VolumeExpirations</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VolumeExpirations</FullTableName><COLUMNNAME>StartTime</COLUMNNAME><DATATYPE>time</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VolumeExpirations</FullTableName><COLUMNNAME>Frequency</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VolumeExpirations</FullTableName><COLUMNNAME>MaxVideoDays</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VolumeExpirations</FullTableName><COLUMNNAME>IsScheduleClips</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VolumeExpirations</FullTableName><COLUMNNAME>IsMotionClips</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VolumeExpirations</FullTableName><COLUMNNAME>IsAlarmClips</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VolumeExports</FullTableName><COLUMNNAME>VolumeExportId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VolumeExports</FullTableName><COLUMNNAME>VolumeId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.VolumeExports</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VolumeExports</FullTableName><COLUMNNAME>StartTime</COLUMNNAME><DATATYPE>time</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VolumeExports</FullTableName><COLUMNNAME>Frequency</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VolumeExports</FullTableName><COLUMNNAME>WillStoreAllExports</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VolumeExports</FullTableName><COLUMNNAME>WillStoreServerExportQueue</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VolumeRetentions</FullTableName><COLUMNNAME>VolumeRetentionId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VolumeRetentions</FullTableName><COLUMNNAME>VolumeId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.VolumeRetentions</FullTableName><COLUMNNAME>ArchiveVolumeId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.VolumeRetentions</FullTableName><COLUMNNAME>BackupVolumeId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.VolumeRetentions</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VolumeRetentions</FullTableName><COLUMNNAME>StartTime</COLUMNNAME><DATATYPE>time</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VolumeRetentions</FullTableName><COLUMNNAME>Frequency</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VolumeRetentions</FullTableName><COLUMNNAME>RetentionDays</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VolumeRetentions</FullTableName><COLUMNNAME>IsScheduleClips</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VolumeRetentions</FullTableName><COLUMNNAME>IsMotionClips</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.VolumeRetentions</FullTableName><COLUMNNAME>IsAlarmClips</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>11</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.Volumes</FullTableName><COLUMNNAME>VolumeId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.Volumes</FullTableName><COLUMNNAME>RecordingServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rs.Volumes</FullTableName><COLUMNNAME>VolumeGuid</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.Volumes</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.Volumes</FullTableName><COLUMNNAME>VolumeType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.Volumes</FullTableName><COLUMNNAME>Name</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>64</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.Volumes</FullTableName><COLUMNNAME>Path</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.Volumes</FullTableName><COLUMNNAME>UncPath</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>2000</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.Volumes</FullTableName><COLUMNNAME>FreeSpacePercentage</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.Volumes</FullTableName><COLUMNNAME>DesiredMinVideoDays</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rs.Volumes</FullTableName><COLUMNNAME>Note</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>11</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.AudioDevices</FullTableName><COLUMNNAME>AudioDeviceId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.AudioDevices</FullTableName><COLUMNNAME>DeviceGuid</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.AudioDevices</FullTableName><COLUMNNAME>Name</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.AudioDevices</FullTableName><COLUMNNAME>DriverType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.AudioDevices</FullTableName><COLUMNNAME>ConnectionType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.AudioDevices</FullTableName><COLUMNNAME>InternalNo</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.AudioDevices</FullTableName><COLUMNNAME>DeviceAttributes</COLUMNNAME><DATATYPE>bigint</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>19</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.AudioDevices</FullTableName><COLUMNNAME>StatusType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.AudioDevices</FullTableName><COLUMNNAME>WasAutoDetected</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.AudioDevices</FullTableName><COLUMNNAME>LastSync</COLUMNNAME><DATATYPE>datetime</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.AudioDevices</FullTableName><COLUMNNAME>Note</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>11</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.AudioDevices</FullTableName><COLUMNNAME>Manufacturer</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>80</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>12</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.AudioDevices</FullTableName><COLUMNNAME>Model</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>13</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.AudioDevices</FullTableName><COLUMNNAME>Quality</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>14</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.AudioDevices</FullTableName><COLUMNNAME>ChannelMode</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>15</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.AudioDevices</FullTableName><COLUMNNAME>IpV4Address</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>16</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.AudioDevices</FullTableName><COLUMNNAME>IpV6Address</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>17</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.AudioDevices</FullTableName><COLUMNNAME>ComPort</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>18</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.AudioDeviceServers</FullTableName><COLUMNNAME>AudioDeviceServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.AudioDeviceServers</FullTableName><COLUMNNAME>AudioDeviceId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rsd.AudioDeviceServers</FullTableName><COLUMNNAME>RecordingServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rsd.CameraEvents</FullTableName><COLUMNNAME>CameraEventId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.CameraEvents</FullTableName><COLUMNNAME>VideoDeviceId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rsd.CameraEvents</FullTableName><COLUMNNAME>CameraEventType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.CameraEvents</FullTableName><COLUMNNAME>Category</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.CameraEvents</FullTableName><COLUMNNAME>EventName</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.CameraEvents</FullTableName><COLUMNNAME>SubEventName</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.CameraEvents</FullTableName><COLUMNNAME>OnEventType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.CameraEvents</FullTableName><COLUMNNAME>OffEventType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.CameraEvents</FullTableName><COLUMNNAME>IsPulsed</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.DeviceTriggers</FullTableName><COLUMNNAME>DeviceTriggerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.DeviceTriggers</FullTableName><COLUMNNAME>VideoDeviceId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rsd.DeviceTriggers</FullTableName><COLUMNNAME>IsInput</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.DeviceTriggers</FullTableName><COLUMNNAME>Name</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>64</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.DeviceTriggers</FullTableName><COLUMNNAME>IdleState</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.DiskDevices</FullTableName><COLUMNNAME>DiskDeviceId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.DiskDevices</FullTableName><COLUMNNAME>DeviceGuid</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.DiskDevices</FullTableName><COLUMNNAME>Name</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.DiskDevices</FullTableName><COLUMNNAME>DriverType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.DiskDevices</FullTableName><COLUMNNAME>ConnectionType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.DiskDevices</FullTableName><COLUMNNAME>InternalNo</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.DiskDevices</FullTableName><COLUMNNAME>DeviceAttributes</COLUMNNAME><DATATYPE>bigint</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>19</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.DiskDevices</FullTableName><COLUMNNAME>StatusType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.DiskDevices</FullTableName><COLUMNNAME>WasAutoDetected</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.DiskDevices</FullTableName><COLUMNNAME>LastSync</COLUMNNAME><DATATYPE>datetime</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.DiskDevices</FullTableName><COLUMNNAME>Note</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>11</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.DiskDevices</FullTableName><COLUMNNAME>TotalSizeMB</COLUMNNAME><DATATYPE>bigint</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>19</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>12</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.DiskDevices</FullTableName><COLUMNNAME>FreeSpaceMB</COLUMNNAME><DATATYPE>bigint</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>19</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>13</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.DiskDevices</FullTableName><COLUMNNAME>IsIndexingEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>14</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.DiskDeviceServers</FullTableName><COLUMNNAME>DiskDeviceServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.DiskDeviceServers</FullTableName><COLUMNNAME>DiskDeviceId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rsd.DiskDeviceServers</FullTableName><COLUMNNAME>RecordingServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rsd.IODevices</FullTableName><COLUMNNAME>IODeviceId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.IODevices</FullTableName><COLUMNNAME>DeviceGuid</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.IODevices</FullTableName><COLUMNNAME>Name</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.IODevices</FullTableName><COLUMNNAME>DriverType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.IODevices</FullTableName><COLUMNNAME>ConnectionType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.IODevices</FullTableName><COLUMNNAME>InternalNo</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.IODevices</FullTableName><COLUMNNAME>DeviceAttributes</COLUMNNAME><DATATYPE>bigint</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>19</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.IODevices</FullTableName><COLUMNNAME>StatusType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.IODevices</FullTableName><COLUMNNAME>WasAutoDetected</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.IODevices</FullTableName><COLUMNNAME>LastSync</COLUMNNAME><DATATYPE>datetime</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.IODevices</FullTableName><COLUMNNAME>Note</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>11</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.IODevices</FullTableName><COLUMNNAME>ComPort</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>12</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.IODevices</FullTableName><COLUMNNAME>InputCount</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>13</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.IODevices</FullTableName><COLUMNNAME>OutputCount</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>14</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.IODevices</FullTableName><COLUMNNAME>Manufacturer</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>80</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>15</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.IODevices</FullTableName><COLUMNNAME>Model</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>16</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.IODevices</FullTableName><COLUMNNAME>IpV4Address</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>17</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.IODevices</FullTableName><COLUMNNAME>IpV6Address</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>18</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.IODeviceServers</FullTableName><COLUMNNAME>IODeviceServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.IODeviceServers</FullTableName><COLUMNNAME>IODeviceId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rsd.IODeviceServers</FullTableName><COLUMNNAME>RecordingServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rsd.MediaProfiles</FullTableName><COLUMNNAME>MediaProfileId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.MediaProfiles</FullTableName><COLUMNNAME>VideoDeviceId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rsd.MediaProfiles</FullTableName><COLUMNNAME>ChannelNo</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.MediaProfiles</FullTableName><COLUMNNAME>Name</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.MediaProfiles</FullTableName><COLUMNNAME>Token</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>64</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.MediaProfiles</FullTableName><COLUMNNAME>SnapshotUrl</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>500</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.MediaProfiles</FullTableName><COLUMNNAME>StreamUrl</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>500</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.MediaProfiles</FullTableName><COLUMNNAME>EncodingProfile</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.MediaProfiles</FullTableName><COLUMNNAME>VideoEncoderType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.MediaProfiles</FullTableName><COLUMNNAME>MaxWidth</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.MediaProfiles</FullTableName><COLUMNNAME>MaxHeight</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>11</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.MediaProfiles</FullTableName><COLUMNNAME>Resolutions</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>-1</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>12</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.MediaProfiles</FullTableName><COLUMNNAME>MinGovLength</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>13</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.MediaProfiles</FullTableName><COLUMNNAME>MaxGovLength</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>14</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.MediaProfiles</FullTableName><COLUMNNAME>GovLength</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>15</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.MediaProfiles</FullTableName><COLUMNNAME>MinFps</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>16</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.MediaProfiles</FullTableName><COLUMNNAME>MaxFps</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>17</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.MediaProfiles</FullTableName><COLUMNNAME>Fps</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>18</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.MediaProfiles</FullTableName><COLUMNNAME>MinQuality</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>19</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.MediaProfiles</FullTableName><COLUMNNAME>MaxQuality</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>20</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.MediaProfiles</FullTableName><COLUMNNAME>Quality</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>21</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.MediaProfiles</FullTableName><COLUMNNAME>MinBitrate</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>22</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.MediaProfiles</FullTableName><COLUMNNAME>MaxBitrate</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>23</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.MediaProfiles</FullTableName><COLUMNNAME>Bitrate</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>24</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.MediaProfiles</FullTableName><COLUMNNAME>IsAudioAvailable</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>25</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.MediaProfiles</FullTableName><COLUMNNAME>AudioEncodingType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>26</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.MediaProfiles</FullTableName><COLUMNNAME>AudioBitrate</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>27</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.MediaProfiles</FullTableName><COLUMNNAME>AudioSampleRate</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>28</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.SerialDevices</FullTableName><COLUMNNAME>SerialDeviceId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.SerialDevices</FullTableName><COLUMNNAME>DeviceGuid</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.SerialDevices</FullTableName><COLUMNNAME>Name</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.SerialDevices</FullTableName><COLUMNNAME>DriverType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.SerialDevices</FullTableName><COLUMNNAME>ConnectionType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.SerialDevices</FullTableName><COLUMNNAME>InternalNo</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.SerialDevices</FullTableName><COLUMNNAME>DeviceAttributes</COLUMNNAME><DATATYPE>bigint</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>19</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.SerialDevices</FullTableName><COLUMNNAME>StatusType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.SerialDevices</FullTableName><COLUMNNAME>WasAutoDetected</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.SerialDevices</FullTableName><COLUMNNAME>LastSync</COLUMNNAME><DATATYPE>datetime</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.SerialDevices</FullTableName><COLUMNNAME>Note</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>11</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.SerialDevices</FullTableName><COLUMNNAME>PortNo</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>12</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.SerialDevices</FullTableName><COLUMNNAME>BaudRate</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>13</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.SerialDevices</FullTableName><COLUMNNAME>DataBits</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>14</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.SerialDevices</FullTableName><COLUMNNAME>ParityType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>15</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.SerialDevices</FullTableName><COLUMNNAME>StopBits</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>16</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.SerialDevices</FullTableName><COLUMNNAME>FlowControl</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>17</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.SerialDeviceServers</FullTableName><COLUMNNAME>SerialDeviceServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.SerialDeviceServers</FullTableName><COLUMNNAME>SerialDeviceId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rsd.SerialDeviceServers</FullTableName><COLUMNNAME>RecordingServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>VideoDeviceId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>ProviderServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>DeviceGuid</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>Name</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>DriverType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>ConnectionType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>InternalNo</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>DeviceAttributes</COLUMNNAME><DATATYPE>bigint</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>19</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>StatusType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>WasAutoDetected</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>LastSync</COLUMNNAME><DATATYPE>datetime</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>11</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>Note</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>12</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>MacAddress</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>13</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>SerialNumber</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>14</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>Manufacturer</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>80</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>15</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>Model</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>16</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>IsPtz</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>17</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>ChannelCount</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>18</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>PresetCount</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>19</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>IpV4Address</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>20</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>IpV6Address</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>21</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>HttpPort</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>22</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>HttpsPort</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>23</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>RtspPort</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>24</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>Firmware</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>60</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>25</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>VideoStandardType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>26</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>SourceFilePath</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>500</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>27</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>OnvifDeviceUrl</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>64</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>28</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>OnvifPtzUrl</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>64</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>29</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>OnvifEventUrl</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>64</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>30</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>StretchSdkVersion</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>32</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>31</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>StretchDriverVersion</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>32</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>32</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>StretchFirmwareVersion</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>32</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>33</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>StretchBootLoaderVersion</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>32</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>34</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>StretchBspVersion</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>32</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>35</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>StretchPciSlotNumber</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>36</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>StretchCardIsPresent</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>37</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>OnBoardEventsLastSync</COLUMNNAME><DATATYPE>datetime</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>38</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>SupportsAudio</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT>((0))</COLUMNDEFAULT><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>39</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDevices</FullTableName><COLUMNNAME>VideoCompressionsArr</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>64</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>40</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDeviceServers</FullTableName><COLUMNNAME>VideoDeviceServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDeviceServers</FullTableName><COLUMNNAME>VideoDeviceId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rsd.VideoDeviceServers</FullTableName><COLUMNNAME>RecordingServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rsf.PrimariesStandbyServers</FullTableName><COLUMNNAME>PrimaryStandbyServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsf.PrimariesStandbyServers</FullTableName><COLUMNNAME>PrimaryRecordingServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rsf.PrimariesStandbyServers</FullTableName><COLUMNNAME>StandbyRecordingServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rsf.PrimaryRecordingServerFailoverConfigs</FullTableName><COLUMNNAME>PrimaryRecordingServerFailoverConfigId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsf.PrimaryRecordingServerFailoverConfigs</FullTableName><COLUMNNAME>FailoverAfterMS</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsf.PrimaryRecordingServerFailoverConfigs</FullTableName><COLUMNNAME>RestoreAfterMS</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsf.StandbyRecordingServerFailoverConfigs</FullTableName><COLUMNNAME>StandbyRecordingServerFailoverConfigId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsf.StandbyRecordingServerFailoverConfigs</FullTableName><COLUMNNAME>PrimaryRecordingServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rsf.StandbyRecordingServerFailoverConfigs</FullTableName><COLUMNNAME>SharePath</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsi.AgentVis</FullTableName><COLUMNNAME>AgentViId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsi.AgentVis</FullTableName><COLUMNNAME>RecordingServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rsi.AgentVis</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsi.AgentVis</FullTableName><COLUMNNAME>EngineAddress</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsi.AgentVis</FullTableName><COLUMNNAME>ProxyLocalPort</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsi.AxisOneClicks</FullTableName><COLUMNNAME>AxisOneClickId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsi.AxisOneClicks</FullTableName><COLUMNNAME>RecordingServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rsi.AxisOneClicks</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsi.AxisOneClicks</FullTableName><COLUMNNAME>DispatchServerAddress</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsi.AxisOneClicks</FullTableName><COLUMNNAME>UserName</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>64</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsi.AxisOneClicks</FullTableName><COLUMNNAME>Password</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsi.AxisOneClicks</FullTableName><COLUMNNAME>ProxyExternalAddress</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsi.AxisOneClicks</FullTableName><COLUMNNAME>ProxyExternalPort</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsi.AxisOneClicks</FullTableName><COLUMNNAME>ProxyInternalAddress</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsi.AxisOneClicks</FullTableName><COLUMNNAME>ProxyInternalPort</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsi.AxisOneClicks</FullTableName><COLUMNNAME>OcccVersion</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>11</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsi.AxisOneClicks</FullTableName><COLUMNNAME>CertificatePath</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>12</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsi.Bolds</FullTableName><COLUMNNAME>BoldId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsi.Bolds</FullTableName><COLUMNNAME>RecordingServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rsi.Bolds</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsi.Bolds</FullTableName><COLUMNNAME>ManitouServerAddress</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>64</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsi.Bolds</FullTableName><COLUMNNAME>ManitouPort</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsi.Bolds</FullTableName><COLUMNNAME>HeartbeatInterval</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsi.BriefCams</FullTableName><COLUMNNAME>BriefCamId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsi.BriefCams</FullTableName><COLUMNNAME>RecordingServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rsi.BriefCams</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsi.BriefCams</FullTableName><COLUMNNAME>Address</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsi.BriefCams</FullTableName><COLUMNNAME>Port</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsi.BriefCams</FullTableName><COLUMNNAME>UserName</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>64</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsi.BriefCams</FullTableName><COLUMNNAME>Password</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsi.BriefCams</FullTableName><COLUMNNAME>AdminPath</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsi.BriefCams</FullTableName><COLUMNNAME>UserPath</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsi.S2s</FullTableName><COLUMNNAME>S2Id</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsi.S2s</FullTableName><COLUMNNAME>RecordingServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rsi.S2s</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsi.S2s</FullTableName><COLUMNNAME>S2ControllerAddress</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>64</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsi.SureViews</FullTableName><COLUMNNAME>SureViewId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsi.SureViews</FullTableName><COLUMNNAME>RecordingServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rsi.SureViews</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsi.SureViews</FullTableName><COLUMNNAME>ImmixDeviceNumber</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsi.SureViews</FullTableName><COLUMNNAME>ImmixSmtpServer</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsi.SureViews</FullTableName><COLUMNNAME>ImmixSmtpPort</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsi.SureViews</FullTableName><COLUMNNAME>ImmixNumberOfSnapshots</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsi.SureViews</FullTableName><COLUMNNAME>ImmixDurationSeconds</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsp.StorageDrives</FullTableName><COLUMNNAME>StoragePoolGuid</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsp.StorageDrives</FullTableName><COLUMNNAME>RecordingServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rsp.StorageDrives</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsp.StorageDrives</FullTableName><COLUMNNAME>DrivePath</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsp.StorageDrives</FullTableName><COLUMNNAME>VideoSpacePercent</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsp.StorageDrives</FullTableName><COLUMNNAME>IsOverflow</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsp.StoragePools</FullTableName><COLUMNNAME>StoragePoolGuid</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsp.StoragePools</FullTableName><COLUMNNAME>RecordingServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rsp.StoragePools</FullTableName><COLUMNNAME>PoolType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsp.StoragePools</FullTableName><COLUMNNAME>WarningLevelPercent</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>rsp.StoragePoolSettings</FullTableName><COLUMNNAME>RecordingServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>rsp.StoragePoolSettings</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>shl.CameraSchedules</FullTableName><COLUMNNAME>CameraScheduleId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>shl.CameraSchedules</FullTableName><COLUMNNAME>ScheduleId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>shl.CameraSchedules</FullTableName><COLUMNNAME>CameraId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>shl.EventTriggerSchedules</FullTableName><COLUMNNAME>EventTriggerScheduleId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>shl.EventTriggerSchedules</FullTableName><COLUMNNAME>ScheduleId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>shl.EventTriggerSchedules</FullTableName><COLUMNNAME>EventTriggerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>shl.ScheduledTimeSegments</FullTableName><COLUMNNAME>ScheduledTimeSegmentId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>shl.ScheduledTimeSegments</FullTableName><COLUMNNAME>ScheduleId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>shl.ScheduledTimeSegments</FullTableName><COLUMNNAME>ScheduleOperationId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>shl.ScheduledTimeSegments</FullTableName><COLUMNNAME>OnDate</COLUMNNAME><DATATYPE>date</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>shl.ScheduledTimeSegments</FullTableName><COLUMNNAME>DayOfWeekType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>shl.ScheduledTimeSegments</FullTableName><COLUMNNAME>StartTime</COLUMNNAME><DATATYPE>time</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>shl.ScheduledTimeSegments</FullTableName><COLUMNNAME>EndTime</COLUMNNAME><DATATYPE>time</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>shl.ScheduleOperations</FullTableName><COLUMNNAME>ScheduleOperationId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>shl.ScheduleOperations</FullTableName><COLUMNNAME>ScheduleId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>shl.ScheduleOperations</FullTableName><COLUMNNAME>OperationType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>shl.Schedules</FullTableName><COLUMNNAME>ScheduleId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>shl.Schedules</FullTableName><COLUMNNAME>RecordingServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>shl.Schedules</FullTableName><COLUMNNAME>ScheduleGuid</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>shl.Schedules</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>shl.Schedules</FullTableName><COLUMNNAME>ResourceType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>shl.Schedules</FullTableName><COLUMNNAME>Name</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>80</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>shl.Schedules</FullTableName><COLUMNNAME>IsDefault</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>shl.Schedules</FullTableName><COLUMNNAME>Note</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>tmp.TmpGuids</FullTableName><COLUMNNAME>Guid</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>tmp.TmpGuids</FullTableName><COLUMNNAME>Value</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>tmp.TmpInts</FullTableName><COLUMNNAME>Id</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>tmp.TmpInts</FullTableName><COLUMNNAME>IntValue</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>tmp.TmpStrings</FullTableName><COLUMNNAME>Guid</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>tmp.TmpStrings</FullTableName><COLUMNNAME>Value</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>1024</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Configurations</FullTableName><COLUMNNAME>ConfigurationId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Configurations</FullTableName><COLUMNNAME>IsAppAutoLoginChecked</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Configurations</FullTableName><COLUMNNAME>IsAppCustomFavoritesChecked</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Configurations</FullTableName><COLUMNNAME>IsAppServersCamerasChecked</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Configurations</FullTableName><COLUMNNAME>IsAppServerIpAddressChecked</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Configurations</FullTableName><COLUMNNAME>IsAppAutoFullscreenChecked</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Configurations</FullTableName><COLUMNNAME>IsPtzChecked</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Configurations</FullTableName><COLUMNNAME>IsLvAutoSequenceChecked</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Configurations</FullTableName><COLUMNNAME>IsLvVideoPanelChecked</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Configurations</FullTableName><COLUMNNAME>IsLvEventPanelChecked</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Configurations</FullTableName><COLUMNNAME>IsLvMapPanelChecked</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>11</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Configurations</FullTableName><COLUMNNAME>IsLvWallPanelChecked</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>12</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Configurations</FullTableName><COLUMNNAME>IsMaxTileCountEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>13</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Configurations</FullTableName><COLUMNNAME>MaxTileCount</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>14</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Configurations</FullTableName><COLUMNNAME>IsAvMapPanelChecked</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>15</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Configurations</FullTableName><COLUMNNAME>IsAvCameraPanelChecked</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>16</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Configurations</FullTableName><COLUMNNAME>PlayersType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>17</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Configurations</FullTableName><COLUMNNAME>IsDbUserConnectionsChecked</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>18</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Configurations</FullTableName><COLUMNNAME>IsDbSearchVideoChecked</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>19</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Configurations</FullTableName><COLUMNNAME>IsDbSearchEventsChecked</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>20</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.PasswordHistory</FullTableName><COLUMNNAME>PasswordHistoryId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.PasswordHistory</FullTableName><COLUMNNAME>UserId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>usr.PasswordHistory</FullTableName><COLUMNNAME>SetOn</COLUMNNAME><DATATYPE>datetime</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.PasswordHistory</FullTableName><COLUMNNAME>Password</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.PasswordPolicies</FullTableName><COLUMNNAME>PasswordPolicyId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.PasswordPolicies</FullTableName><COLUMNNAME>PolicyJson</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>4000</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Roles</FullTableName><COLUMNNAME>RoleId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Roles</FullTableName><COLUMNNAME>Identifier</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>20</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Roles</FullTableName><COLUMNNAME>Name</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>60</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserCameras</FullTableName><COLUMNNAME>UserCameraId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserCameras</FullTableName><COLUMNNAME>ConfigurationId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserCameras</FullTableName><COLUMNNAME>CameraId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserCameras</FullTableName><COLUMNNAME>IsLiveChecked</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserCameras</FullTableName><COLUMNNAME>IsPlaybackChecked</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserCameras</FullTableName><COLUMNNAME>IsAudioChecked</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserCameras</FullTableName><COLUMNNAME>IsExportChecked</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserCameras</FullTableName><COLUMNNAME>IsSnapshotChecked</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserCameras</FullTableName><COLUMNNAME>IsLightChecked</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserCameras</FullTableName><COLUMNNAME>IsPtzChecked</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserCameras</FullTableName><COLUMNNAME>PlaybackTimeout</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>11</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserCameras</FullTableName><COLUMNNAME>IsFavorite</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>12</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserEvents</FullTableName><COLUMNNAME>UserEventId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserEvents</FullTableName><COLUMNNAME>ConfigurationId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserEvents</FullTableName><COLUMNNAME>EventType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserEvents</FullTableName><COLUMNNAME>IsChecked</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserEventTriggers</FullTableName><COLUMNNAME>UserEventTriggerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserEventTriggers</FullTableName><COLUMNNAME>ConfigurationId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserEventTriggers</FullTableName><COLUMNNAME>EventTriggerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserEventTriggers</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserEventTriggers</FullTableName><COLUMNNAME>WillExecute</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserEventTriggers</FullTableName><COLUMNNAME>IsFavorite</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserInputs</FullTableName><COLUMNNAME>UserInputId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserInputs</FullTableName><COLUMNNAME>ConfigurationId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserInputs</FullTableName><COLUMNNAME>IOInputId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserInputs</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserInputs</FullTableName><COLUMNNAME>IsFavorite</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserModules</FullTableName><COLUMNNAME>UserModuleId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserModules</FullTableName><COLUMNNAME>ConfigurationId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserModules</FullTableName><COLUMNNAME>ModuleType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserModules</FullTableName><COLUMNNAME>HasAccess</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserModules</FullTableName><COLUMNNAME>IsSetAtStartup</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserOutputs</FullTableName><COLUMNNAME>UserOutputId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserOutputs</FullTableName><COLUMNNAME>ConfigurationId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserOutputs</FullTableName><COLUMNNAME>IOOutputId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserOutputs</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserOutputs</FullTableName><COLUMNNAME>WillExecute</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserOutputs</FullTableName><COLUMNNAME>IsFavorite</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserPasswordPolicies</FullTableName><COLUMNNAME>UserId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserPasswordPolicies</FullTableName><COLUMNNAME>PolicyJson</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>4000</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserPresets</FullTableName><COLUMNNAME>UserPresetId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserPresets</FullTableName><COLUMNNAME>ConfigurationId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserPresets</FullTableName><COLUMNNAME>CameraId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserPresets</FullTableName><COLUMNNAME>PresetIndex</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserPresets</FullTableName><COLUMNNAME>IsSetChecked</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserPresets</FullTableName><COLUMNNAME>IsShowChecked</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserRoles</FullTableName><COLUMNNAME>UserRoleId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserRoles</FullTableName><COLUMNNAME>ConfigurationId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserRoles</FullTableName><COLUMNNAME>RoleId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>usr.Users</FullTableName><COLUMNNAME>UserId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Users</FullTableName><COLUMNNAME>ConfigurationId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>usr.Users</FullTableName><COLUMNNAME>UserGuid</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Users</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Users</FullTableName><COLUMNNAME>IsAdmin</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Users</FullTableName><COLUMNNAME>RemoteAccess</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Users</FullTableName><COLUMNNAME>ActiveDirectoryId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>usr.Users</FullTableName><COLUMNNAME>SubDomain</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Users</FullTableName><COLUMNNAME>Username</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>500</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Users</FullTableName><COLUMNNAME>Password</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Users</FullTableName><COLUMNNAME>IsDefaultPassword</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>11</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Users</FullTableName><COLUMNNAME>PtzPriority</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>12</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Users</FullTableName><COLUMNNAME>IsManaged</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>13</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Users</FullTableName><COLUMNNAME>ExpiresOn</COLUMNNAME><DATATYPE>datetime</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>14</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Users</FullTableName><COLUMNNAME>Note</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>15</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Users</FullTableName><COLUMNNAME>LastName</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>250</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>16</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Users</FullTableName><COLUMNNAME>Email</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>250</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>17</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Users</FullTableName><COLUMNNAME>ResetOn</COLUMNNAME><DATATYPE>datetime</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>18</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Users</FullTableName><COLUMNNAME>FirstName</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>19</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Users</FullTableName><COLUMNNAME>Phone</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>20</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Users</FullTableName><COLUMNNAME>Address</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>21</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Users</FullTableName><COLUMNNAME>Cloud</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT>((0))</COLUMNDEFAULT><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>22</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.Users</FullTableName><COLUMNNAME>MsOwnerGuid</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>23</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserServers</FullTableName><COLUMNNAME>UserServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserServers</FullTableName><COLUMNNAME>ConfigurationId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserServers</FullTableName><COLUMNNAME>RecordingServerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserServers</FullTableName><COLUMNNAME>IsAdminChecked</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserServers</FullTableName><COLUMNNAME>IsApiChecked</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserServers</FullTableName><COLUMNNAME>IsEventsChecked</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserServers</FullTableName><COLUMNNAME>PtzPriorityType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserServers</FullTableName><COLUMNNAME>IsFavorite</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserViews</FullTableName><COLUMNNAME>UserViewId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserViews</FullTableName><COLUMNNAME>ConfigurationId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserViews</FullTableName><COLUMNNAME>ViewId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserViews</FullTableName><COLUMNNAME>IsFavorite</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserViews</FullTableName><COLUMNNAME>Navigation</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT>((0))</COLUMNDEFAULT><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserViews</FullTableName><COLUMNNAME>ShowUrl</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT>((0))</COLUMNDEFAULT><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserViews</FullTableName><COLUMNNAME>LiveView</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT>((0))</COLUMNDEFAULT><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>usr.UserViews</FullTableName><COLUMNNAME>Playback</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT>((0))</COLUMNDEFAULT><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.CameraViews</FullTableName><COLUMNNAME>CameraViewId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.CameraViews</FullTableName><COLUMNNAME>ViewId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>vw.CameraViews</FullTableName><COLUMNNAME>CameraId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>vw.CameraViews</FullTableName><COLUMNNAME>FitOption</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.CameraViewTriggers</FullTableName><COLUMNNAME>CameraViewTriggerId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.CameraViewTriggers</FullTableName><COLUMNNAME>ViewId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>vw.CameraViewTriggers</FullTableName><COLUMNNAME>CameraViewId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>vw.CameraViewTriggers</FullTableName><COLUMNNAME>IoOutputId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>vw.FixedTiles</FullTableName><COLUMNNAME>FixedTileId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.FixedTiles</FullTableName><COLUMNNAME>ViewId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>vw.FixedTiles</FullTableName><COLUMNNAME>TileId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>vw.FixedTiles</FullTableName><COLUMNNAME>PositionX</COLUMNNAME><DATATYPE>float</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>53</NUMERICPRECISION><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.FixedTiles</FullTableName><COLUMNNAME>PositionY</COLUMNNAME><DATATYPE>float</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>53</NUMERICPRECISION><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.FixedTiles</FullTableName><COLUMNNAME>Width</COLUMNNAME><DATATYPE>float</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>53</NUMERICPRECISION><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.FixedTiles</FullTableName><COLUMNNAME>Height</COLUMNNAME><DATATYPE>float</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>53</NUMERICPRECISION><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.MapImages</FullTableName><COLUMNNAME>MapImageId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.MapImages</FullTableName><COLUMNNAME>ImageBytes</COLUMNNAME><DATATYPE>varbinary</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>-1</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.MapImages</FullTableName><COLUMNNAME>CompressionType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT>((0))</COLUMNDEFAULT><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.MapImages</FullTableName><COLUMNNAME>FileSize</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT>((0))</COLUMNDEFAULT><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.MapImages</FullTableName><COLUMNNAME>Md5</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.MapImages</FullTableName><COLUMNNAME>Height</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT>((0))</COLUMNDEFAULT><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.MapImages</FullTableName><COLUMNNAME>Width</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT>((0))</COLUMNDEFAULT><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.Maps</FullTableName><COLUMNNAME>MapId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.Maps</FullTableName><COLUMNNAME>MapImageId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>vw.Maps</FullTableName><COLUMNNAME>MapGuid</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.Maps</FullTableName><COLUMNNAME>MapType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.Maps</FullTableName><COLUMNNAME>Name</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.Maps</FullTableName><COLUMNNAME>Address</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.Maps</FullTableName><COLUMNNAME>Latitude</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>20</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.Maps</FullTableName><COLUMNNAME>Longitude</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>20</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.Maps</FullTableName><COLUMNNAME>CompressionType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.Maps</FullTableName><COLUMNNAME>FileSize</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.Maps</FullTableName><COLUMNNAME>Md5</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>128</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>11</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.Maps</FullTableName><COLUMNNAME>Height</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>12</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.Maps</FullTableName><COLUMNNAME>Width</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>13</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.MapViewCameras</FullTableName><COLUMNNAME>MapViewCameraId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.MapViewCameras</FullTableName><COLUMNNAME>ViewId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>vw.MapViewCameras</FullTableName><COLUMNNAME>MapViewId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>vw.MapViewCameras</FullTableName><COLUMNNAME>CameraId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>vw.MapViewCameras</FullTableName><COLUMNNAME>PositionX</COLUMNNAME><DATATYPE>float</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>53</NUMERICPRECISION><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.MapViewCameras</FullTableName><COLUMNNAME>PositionY</COLUMNNAME><DATATYPE>float</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>53</NUMERICPRECISION><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.MapViewLinks</FullTableName><COLUMNNAME>MapViewLinkId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.MapViewLinks</FullTableName><COLUMNNAME>ViewId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>vw.MapViewLinks</FullTableName><COLUMNNAME>MapViewId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>vw.MapViewLinks</FullTableName><COLUMNNAME>LinkedViewId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>vw.MapViewLinks</FullTableName><COLUMNNAME>PositionX</COLUMNNAME><DATATYPE>float</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>53</NUMERICPRECISION><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.MapViewLinks</FullTableName><COLUMNNAME>PositionY</COLUMNNAME><DATATYPE>float</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>53</NUMERICPRECISION><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.MapViews</FullTableName><COLUMNNAME>MapViewId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.MapViews</FullTableName><COLUMNNAME>ViewId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>vw.MapViews</FullTableName><COLUMNNAME>MapId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>vw.Pins</FullTableName><COLUMNNAME>PinId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.Pins</FullTableName><COLUMNNAME>ViewId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>vw.Pins</FullTableName><COLUMNNAME>MapViewId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>vw.Pins</FullTableName><COLUMNNAME>Latitude</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>20</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.Pins</FullTableName><COLUMNNAME>Longitude</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>20</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.Pins</FullTableName><COLUMNNAME>PositionX</COLUMNNAME><DATATYPE>float</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>53</NUMERICPRECISION><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.Pins</FullTableName><COLUMNNAME>PositionY</COLUMNNAME><DATATYPE>float</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>53</NUMERICPRECISION><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.ResponsiveTiles</FullTableName><COLUMNNAME>ResponsiveTileId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.ResponsiveTiles</FullTableName><COLUMNNAME>ViewId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>vw.ResponsiveTiles</FullTableName><COLUMNNAME>TileId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>vw.ResponsiveTiles</FullTableName><COLUMNNAME>ParentTileId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>vw.ResponsiveTiles</FullTableName><COLUMNNAME>LayoutStyle</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.ResponsiveTiles</FullTableName><COLUMNNAME>Ordinal</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.ResponsiveTiles</FullTableName><COLUMNNAME>AbsoluteWidth</COLUMNNAME><DATATYPE>float</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>53</NUMERICPRECISION><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.ResponsiveTiles</FullTableName><COLUMNNAME>AbsoluteHeight</COLUMNNAME><DATATYPE>float</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>53</NUMERICPRECISION><NUMERICSCALE /><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.ResponsiveTiles</FullTableName><COLUMNNAME>PositionX</COLUMNNAME><DATATYPE>float</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>53</NUMERICPRECISION><NUMERICSCALE /><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.ResponsiveTiles</FullTableName><COLUMNNAME>PositionY</COLUMNNAME><DATATYPE>float</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>53</NUMERICPRECISION><NUMERICSCALE /><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.ResponsiveTiles</FullTableName><COLUMNNAME>Row</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>11</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.ResponsiveTiles</FullTableName><COLUMNNAME>Column</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>12</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.ResponsiveTiles</FullTableName><COLUMNNAME>RowSpan</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>13</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.ResponsiveTiles</FullTableName><COLUMNNAME>ColumnSpan</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>14</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.Tiles</FullTableName><COLUMNNAME>TileId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.Tiles</FullTableName><COLUMNNAME>ViewId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.Tiles</FullTableName><COLUMNNAME>CameraViewId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>vw.Tiles</FullTableName><COLUMNNAME>MapViewId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>vw.Tiles</FullTableName><COLUMNNAME>WebViewId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>vw.Tiles</FullTableName><COLUMNNAME>WallViewId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>vw.Views</FullTableName><COLUMNNAME>ViewId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.Views</FullTableName><COLUMNNAME>RelatedViewId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>vw.Views</FullTableName><COLUMNNAME>ViewGuid</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.Views</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.Views</FullTableName><COLUMNNAME>Name</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>500</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.Views</FullTableName><COLUMNNAME>IsTemplate</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.Views</FullTableName><COLUMNNAME>DeclaredType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.Views</FullTableName><COLUMNNAME>LayoutStyle</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.Views</FullTableName><COLUMNNAME>Width</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.Views</FullTableName><COLUMNNAME>Height</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.Views</FullTableName><COLUMNNAME>CreatedByUserId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>11</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>vw.Views</FullTableName><COLUMNNAME>AspectRatio</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>12</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.Views</FullTableName><COLUMNNAME>IsInTour</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>13</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.Views</FullTableName><COLUMNNAME>DwellTimeSeconds</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>14</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.Views</FullTableName><COLUMNNAME>Fps</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>15</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.Views</FullTableName><COLUMNNAME>Quality</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>16</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.Views</FullTableName><COLUMNNAME>ShowCones</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>17</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.Views</FullTableName><COLUMNNAME>ShowLabel</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>18</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.Views</FullTableName><COLUMNNAME>ScaleToFit</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>19</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.Views</FullTableName><COLUMNNAME>ZoomLevel</COLUMNNAME><DATATYPE>float</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>53</NUMERICPRECISION><NUMERICSCALE /><ORDINALPOSITION>20</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.Views</FullTableName><COLUMNNAME>PreviewIcon</COLUMNNAME><DATATYPE>varbinary</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>-1</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>21</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.Views</FullTableName><COLUMNNAME>Note</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>22</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.WallViews</FullTableName><COLUMNNAME>WallViewId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.WallViews</FullTableName><COLUMNNAME>ViewId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>vw.WallViews</FullTableName><COLUMNNAME>WvAgentDisplayId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>vw.WallViews</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.WallViews</FullTableName><COLUMNNAME>AspectRatio</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.WallViews</FullTableName><COLUMNNAME>RowCount</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.WallViews</FullTableName><COLUMNNAME>ColumnCount</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.WebViews</FullTableName><COLUMNNAME>WebViewId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.WebViews</FullTableName><COLUMNNAME>ViewId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>vw.WebViews</FullTableName><COLUMNNAME>WebUrl</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>2048</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vw.WebViews</FullTableName><COLUMNNAME>IsAudioEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT>((1))</COLUMNDEFAULT><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vwt.TemplateTiles</FullTableName><COLUMNNAME>TemplateTileId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vwt.TemplateTiles</FullTableName><COLUMNNAME>ViewTemplateId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>vwt.TemplateTiles</FullTableName><COLUMNNAME>ParentTemplateTileId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>vwt.TemplateTiles</FullTableName><COLUMNNAME>TileType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vwt.TemplateTiles</FullTableName><COLUMNNAME>PointX</COLUMNNAME><DATATYPE>float</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>53</NUMERICPRECISION><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vwt.TemplateTiles</FullTableName><COLUMNNAME>PointY</COLUMNNAME><DATATYPE>float</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>53</NUMERICPRECISION><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vwt.TemplateTiles</FullTableName><COLUMNNAME>Height</COLUMNNAME><DATATYPE>float</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>53</NUMERICPRECISION><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vwt.TemplateTiles</FullTableName><COLUMNNAME>Width</COLUMNNAME><DATATYPE>float</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>53</NUMERICPRECISION><NUMERICSCALE /><ORDINALPOSITION>8</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vwt.TemplateTiles</FullTableName><COLUMNNAME>Row</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>9</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vwt.TemplateTiles</FullTableName><COLUMNNAME>Column</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>10</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vwt.TemplateTiles</FullTableName><COLUMNNAME>RowSpan</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>11</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vwt.TemplateTiles</FullTableName><COLUMNNAME>ColumnSpan</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>12</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vwt.ViewTemplates</FullTableName><COLUMNNAME>ViewTemplateId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vwt.ViewTemplates</FullTableName><COLUMNNAME>ViewTemplateGuid</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vwt.ViewTemplates</FullTableName><COLUMNNAME>Name</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>64</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vwt.ViewTemplates</FullTableName><COLUMNNAME>ViewType</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vwt.ViewTemplates</FullTableName><COLUMNNAME>LayoutStyle</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>vwt.ViewTemplates</FullTableName><COLUMNNAME>ImageBytesBase64</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>-1</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>wall.WallViewAgents</FullTableName><COLUMNNAME>WallViewAgentId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>wall.WallViewAgents</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>wall.WallViewAgents</FullTableName><COLUMNNAME>AgentGuid</COLUMNNAME><DATATYPE>uniqueidentifier</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>wall.WallViewAgents</FullTableName><COLUMNNAME>Name</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>64</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>wall.WallViewAgents</FullTableName><COLUMNNAME>HostIp</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>64</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>wall.WallViewAgents</FullTableName><COLUMNNAME>PortNo</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>wall.WallViewAgents</FullTableName><COLUMNNAME>Note</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>256</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>wall.WvAgentDisplays</FullTableName><COLUMNNAME>WvAgentDisplayId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>1</ORDINALPOSITION><ISPRIMARYKEY>YES</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>wall.WvAgentDisplays</FullTableName><COLUMNNAME>WallViewAgentId</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>2</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>YES</ISFOREIGNKEY></Item><Item><FullTableName>wall.WvAgentDisplays</FullTableName><COLUMNNAME>IsEnabled</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>3</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>wall.WvAgentDisplays</FullTableName><COLUMNNAME>IsPrimary</COLUMNNAME><DATATYPE>bit</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>4</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>wall.WvAgentDisplays</FullTableName><COLUMNNAME>DisplayNo</COLUMNNAME><DATATYPE>int</DATATYPE><ISNULLABLE>NO</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH /><NUMERICPRECISION>10</NUMERICPRECISION><NUMERICSCALE>0</NUMERICSCALE><ORDINALPOSITION>5</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>wall.WvAgentDisplays</FullTableName><COLUMNNAME>DeviceName</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>100</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>6</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item><Item><FullTableName>wall.WvAgentDisplays</FullTableName><COLUMNNAME>DisplayName</COLUMNNAME><DATATYPE>nvarchar</DATATYPE><ISNULLABLE>YES</ISNULLABLE><COLUMNDEFAULT /><CHARACTERMAXIMUMLENGTH>100</CHARACTERMAXIMUMLENGTH><NUMERICPRECISION /><NUMERICSCALE /><ORDINALPOSITION>7</ORDINALPOSITION><ISPRIMARYKEY>NO</ISPRIMARYKEY><ISFOREIGNKEY>NO</ISFOREIGNKEY></Item></EnhancedTablesandColumns><EnhancedForeignKeyRelationships><Item><FKConstraintName>FK_Cameras_AdvancedSettingId</FKConstraintName><FKTable>cam.Cameras</FKTable><FKColumn>AdvancedSettingId</FKColumn><ReferencedTable>cam.AdvancedSettings</ReferencedTable><ReferencedColumn>AdvancedSettingId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_Cameras_Camera360LensId</FKConstraintName><FKTable>cam.Cameras</FKTable><FKColumn>Camera360LensId</FKColumn><ReferencedTable>cam.Camera360Lens</ReferencedTable><ReferencedColumn>Camera360LensId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_Camera_ConeId</FKConstraintName><FKTable>cam.Cameras</FKTable><FKColumn>ConeId</FKColumn><ReferencedTable>cam.Cones</ReferencedTable><ReferencedColumn>ConeId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_Cameras_EdgeSettingId</FKConstraintName><FKTable>cam.Cameras</FKTable><FKColumn>EdgeSettingId</FKColumn><ReferencedTable>cam.EdgeSettings</ReferencedTable><ReferencedColumn>EdgeSettingId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_Cameras_IpSettingId</FKConstraintName><FKTable>cam.Cameras</FKTable><FKColumn>IpSettingId</FKColumn><ReferencedTable>cam.IpSettings</ReferencedTable><ReferencedColumn>IpSettingId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_Cameras_PtzSettingId</FKConstraintName><FKTable>cam.Cameras</FKTable><FKColumn>PtzSettingId</FKColumn><ReferencedTable>cam.PtzSettings</ReferencedTable><ReferencedColumn>PtzSettingId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_Camera_RecordingServerId</FKConstraintName><FKTable>cam.Cameras</FKTable><FKColumn>RecordingServerId</FKColumn><ReferencedTable>rs.RecordingServers</ReferencedTable><ReferencedColumn>RecordingServerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_Cameras_RecordingSettingId</FKConstraintName><FKTable>cam.Cameras</FKTable><FKColumn>RecordingSettingId</FKColumn><ReferencedTable>cam.RecordingSettings</ReferencedTable><ReferencedColumn>RecordingSettingId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_Cameras_StreamProcessingId</FKConstraintName><FKTable>cam.Cameras</FKTable><FKColumn>StreamProcessingId</FKColumn><ReferencedTable>cam.StreamProcessings</ReferencedTable><ReferencedColumn>StreamProcessingId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_Cameras_StretchSettingId</FKConstraintName><FKTable>cam.Cameras</FKTable><FKColumn>StretchSettingId</FKColumn><ReferencedTable>cam.StretchSettings</ReferencedTable><ReferencedColumn>StretchSettingId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_Camera_VideoDeviceId</FKConstraintName><FKTable>cam.Cameras</FKTable><FKColumn>VideoDeviceId</FKColumn><ReferencedTable>rsd.VideoDevices</ReferencedTable><ReferencedColumn>VideoDeviceId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_CameraVolume_CameraId</FKConstraintName><FKTable>cam.CameraVolumes</FKTable><FKColumn>CameraId</FKColumn><ReferencedTable>cam.Cameras</ReferencedTable><ReferencedColumn>CameraId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_CameraVolume_VolumeId</FKConstraintName><FKTable>cam.CameraVolumes</FKTable><FKColumn>VolumeId</FKColumn><ReferencedTable>rs.Volumes</ReferencedTable><ReferencedColumn>VolumeId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_CamEvents_CameraEventId</FKConstraintName><FKTable>cam.CamEvents</FKTable><FKColumn>CameraEventId</FKColumn><ReferencedTable>rsd.CameraEvents</ReferencedTable><ReferencedColumn>CameraEventId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_CamEvents_CameraId</FKConstraintName><FKTable>cam.CamEvents</FKTable><FKColumn>CameraId</FKColumn><ReferencedTable>cam.Cameras</ReferencedTable><ReferencedColumn>CameraId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_MediaStreams_AudioDeviceId</FKConstraintName><FKTable>cam.MediaStreams</FKTable><FKColumn>AudioDeviceId</FKColumn><ReferencedTable>rsd.AudioDevices</ReferencedTable><ReferencedColumn>AudioDeviceId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_MediaStreams_CameraId</FKConstraintName><FKTable>cam.MediaStreams</FKTable><FKColumn>CameraId</FKColumn><ReferencedTable>cam.Cameras</ReferencedTable><ReferencedColumn>CameraId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_MotionZones_CameraId</FKConstraintName><FKTable>cam.MotionZones</FKTable><FKColumn>CameraId</FKColumn><ReferencedTable>cam.Cameras</ReferencedTable><ReferencedColumn>CameraId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_Presets_PtzSettingId</FKConstraintName><FKTable>cam.Presets</FKTable><FKColumn>PtzSettingId</FKColumn><ReferencedTable>cam.PtzSettings</ReferencedTable><ReferencedColumn>PtzSettingId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_ProCamps_MediaStreamId</FKConstraintName><FKTable>cam.ProCamps</FKTable><FKColumn>MediaStreamId</FKColumn><ReferencedTable>cam.MediaStreams</ReferencedTable><ReferencedColumn>MediaStreamId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_Retention_CameraId</FKConstraintName><FKTable>cam.RetentionPolicies</FKTable><FKColumn>CameraId</FKColumn><ReferencedTable>cam.Cameras</ReferencedTable><ReferencedColumn>CameraId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_VideoOverlays_StreamProcessingId</FKConstraintName><FKTable>cam.VideoOverlays</FKTable><FKColumn>StreamProcessingId</FKColumn><ReferencedTable>cam.StreamProcessings</ReferencedTable><ReferencedColumn>StreamProcessingId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_RegionMembers_RecordingServerId</FKConstraintName><FKTable>cfg.RegionMembers</FKTable><FKColumn>RecordingServerId</FKColumn><ReferencedTable>rs.RecordingServers</ReferencedTable><ReferencedColumn>RecordingServerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_RegionMembers_RegionHierarchyId</FKConstraintName><FKTable>cfg.RegionMembers</FKTable><FKColumn>RegionHierarchyId</FKColumn><ReferencedTable>cfg.RegionsHierarchy</ReferencedTable><ReferencedColumn>RegionHierarchyId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_RegionMembers_ViewId</FKConstraintName><FKTable>cfg.RegionMembers</FKTable><FKColumn>ViewId</FKColumn><ReferencedTable>vw.Views</ReferencedTable><ReferencedColumn>ViewId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_RegionsHierarchy_ParentId</FKConstraintName><FKTable>cfg.RegionsHierarchy</FKTable><FKColumn>ParentId</FKColumn><ReferencedTable>cfg.RegionsHierarchy</ReferencedTable><ReferencedColumn>RegionHierarchyId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_RegionsHierarchy_RegionTypeId</FKConstraintName><FKTable>cfg.RegionsHierarchy</FKTable><FKColumn>RegionTypeId</FKColumn><ReferencedTable>cfg.RegionTypes</ReferencedTable><ReferencedColumn>RegionTypeId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_Certificate_LocalSecureWebServerId</FKConstraintName><FKTable>com.Certificates</FKTable><FKColumn>LocalSecureWebServerId</FKColumn><ReferencedTable>com.LocalSecureWebServers</ReferencedTable><ReferencedColumn>LocalSecureWebServerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_BandwidthControls_RecordingServerId</FKConstraintName><FKTable>cso.BandwidthControls</FKTable><FKColumn>RecordingServerId</FKColumn><ReferencedTable>rs.RecordingServers</ReferencedTable><ReferencedColumn>RecordingServerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_GroupMember_GroupId</FKConstraintName><FKTable>grp.GroupMembers</FKTable><FKColumn>GroupId</FKColumn><ReferencedTable>grp.Groups</ReferencedTable><ReferencedColumn>GroupId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_GroupMember_UserId</FKConstraintName><FKTable>grp.GroupMembers</FKTable><FKColumn>UserId</FKColumn><ReferencedTable>usr.Users</ReferencedTable><ReferencedColumn>UserId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_Groups_ActiveDirectoryId</FKConstraintName><FKTable>grp.Groups</FKTable><FKColumn>ActiveDirectoryId</FKColumn><ReferencedTable>com.ActiveDirectories</ReferencedTable><ReferencedColumn>ActiveDirectoryId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_Groups_ConfigurationId</FKConstraintName><FKTable>grp.Groups</FKTable><FKColumn>ConfigurationId</FKColumn><ReferencedTable>usr.Configurations</ReferencedTable><ReferencedColumn>ConfigurationId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_Groups_ParentId</FKConstraintName><FKTable>grp.Groups</FKTable><FKColumn>ParentId</FKColumn><ReferencedTable>grp.Groups</ReferencedTable><ReferencedColumn>GroupId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_PushServerStatus_RecordingServerId</FKConstraintName><FKTable>ms.PushServerStatus</FKTable><FKColumn>RecordingServerId</FKColumn><ReferencedTable>rs.RecordingServers</ReferencedTable><ReferencedColumn>RecordingServerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_PushToServers_RecordingServerId</FKConstraintName><FKTable>ms.PushToServers</FKTable><FKColumn>RecordingServerId</FKColumn><ReferencedTable>rs.RecordingServers</ReferencedTable><ReferencedColumn>RecordingServerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_DefaultBehaviors_RecordingServerId</FKConstraintName><FKTable>rs.DefaultBehaviors</FKTable><FKColumn>RecordingServerId</FKColumn><ReferencedTable>rs.RecordingServers</ReferencedTable><ReferencedColumn>RecordingServerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_EventNotificationSetting_EventNotificationId</FKConstraintName><FKTable>rs.EventNotificationSettings</FKTable><FKColumn>EventNotificationId</FKColumn><ReferencedTable>rs.EventNotifications</ReferencedTable><ReferencedColumn>EventNotificationId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_EventsToLog_RecordingServerId</FKConstraintName><FKTable>rs.EventsToLog</FKTable><FKColumn>RecordingServerId</FKColumn><ReferencedTable>rs.RecordingServers</ReferencedTable><ReferencedColumn>RecordingServerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_EventTriggerAction_CameraId</FKConstraintName><FKTable>rs.EventTriggerActions</FKTable><FKColumn>CameraId</FKColumn><ReferencedTable>cam.Cameras</ReferencedTable><ReferencedColumn>CameraId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_EventTriggerAction_EventTriggerId</FKConstraintName><FKTable>rs.EventTriggerActions</FKTable><FKColumn>EventTriggerId</FKColumn><ReferencedTable>rs.EventTriggers</ReferencedTable><ReferencedColumn>EventTriggerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_EventTriggerAction_IOTriggerId</FKConstraintName><FKTable>rs.EventTriggerActions</FKTable><FKColumn>IOTriggerId</FKColumn><ReferencedTable>rs.IOTriggers</ReferencedTable><ReferencedColumn>IOTriggerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_EventTriggerAction_VolumeId</FKConstraintName><FKTable>rs.EventTriggerActions</FKTable><FKColumn>VolumeId</FKColumn><ReferencedTable>rs.Volumes</ReferencedTable><ReferencedColumn>VolumeId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_EventTriggerResponseAction_EventTriggerActionId</FKConstraintName><FKTable>rs.EventTriggerResponseActions</FKTable><FKColumn>EventTriggerActionId</FKColumn><ReferencedTable>rs.EventTriggerActions</ReferencedTable><ReferencedColumn>EventTriggerActionId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_EventTriggerResponseAction_EventTriggerId</FKConstraintName><FKTable>rs.EventTriggerResponseActions</FKTable><FKColumn>EventTriggerId</FKColumn><ReferencedTable>rs.EventTriggers</ReferencedTable><ReferencedColumn>EventTriggerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_EventTriggerResponseAction_EventTriggerResponseId</FKConstraintName><FKTable>rs.EventTriggerResponseActions</FKTable><FKColumn>EventTriggerResponseId</FKColumn><ReferencedTable>rs.EventTriggerResponses</ReferencedTable><ReferencedColumn>EventTriggerResponseId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_EventTrigger_EventTriggerResponseId</FKConstraintName><FKTable>rs.EventTriggers</FKTable><FKColumn>EventTriggerResponseId</FKColumn><ReferencedTable>rs.EventTriggerResponses</ReferencedTable><ReferencedColumn>EventTriggerResponseId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_EventTrigger_RecordingServerId</FKConstraintName><FKTable>rs.EventTriggers</FKTable><FKColumn>RecordingServerId</FKColumn><ReferencedTable>rs.RecordingServers</ReferencedTable><ReferencedColumn>RecordingServerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_EventTriggerSource_CameraId</FKConstraintName><FKTable>rs.EventTriggerSources</FKTable><FKColumn>CameraId</FKColumn><ReferencedTable>cam.Cameras</ReferencedTable><ReferencedColumn>CameraId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_EventTriggerSource_EventTriggerId</FKConstraintName><FKTable>rs.EventTriggerSources</FKTable><FKColumn>EventTriggerId</FKColumn><ReferencedTable>rs.EventTriggers</ReferencedTable><ReferencedColumn>EventTriggerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_EventTriggerSource_IOTriggerId</FKConstraintName><FKTable>rs.EventTriggerSources</FKTable><FKColumn>IOTriggerId</FKColumn><ReferencedTable>rs.IOTriggers</ReferencedTable><ReferencedColumn>IOTriggerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_EventTriggerSource_UserId</FKConstraintName><FKTable>rs.EventTriggerSources</FKTable><FKColumn>UserId</FKColumn><ReferencedTable>usr.Users</ReferencedTable><ReferencedColumn>UserId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_EventTriggerSource_VolumeId</FKConstraintName><FKTable>rs.EventTriggerSources</FKTable><FKColumn>VolumeId</FKColumn><ReferencedTable>rs.Volumes</ReferencedTable><ReferencedColumn>VolumeId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_IoInput_IOTriggerId</FKConstraintName><FKTable>rs.IoInputs</FKTable><FKColumn>IOTriggerId</FKColumn><ReferencedTable>rs.IOTriggers</ReferencedTable><ReferencedColumn>IOTriggerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_IoOutputCamera_CameraId</FKConstraintName><FKTable>rs.IoOutputCameras</FKTable><FKColumn>CameraId</FKColumn><ReferencedTable>cam.Cameras</ReferencedTable><ReferencedColumn>CameraId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_IoOutputCamera_IoOutputId</FKConstraintName><FKTable>rs.IoOutputCameras</FKTable><FKColumn>IoOutputId</FKColumn><ReferencedTable>rs.IoOutputs</ReferencedTable><ReferencedColumn>IoOutputId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_IoOutputCamera_IoTriggerId</FKConstraintName><FKTable>rs.IoOutputCameras</FKTable><FKColumn>IoTriggerId</FKColumn><ReferencedTable>rs.IOTriggers</ReferencedTable><ReferencedColumn>IOTriggerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_IoOutput_IOTriggerId</FKConstraintName><FKTable>rs.IoOutputs</FKTable><FKColumn>IOTriggerId</FKColumn><ReferencedTable>rs.IOTriggers</ReferencedTable><ReferencedColumn>IOTriggerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_IOTrigger_IODeviceId</FKConstraintName><FKTable>rs.IOTriggers</FKTable><FKColumn>IODeviceId</FKColumn><ReferencedTable>rsd.IODevices</ReferencedTable><ReferencedColumn>IODeviceId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_IOTrigger_RecordingServerId</FKConstraintName><FKTable>rs.IOTriggers</FKTable><FKColumn>RecordingServerId</FKColumn><ReferencedTable>rs.RecordingServers</ReferencedTable><ReferencedColumn>RecordingServerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_Logger_LogSettingId</FKConstraintName><FKTable>rs.Loggers</FKTable><FKColumn>LogSettingId</FKColumn><ReferencedTable>rs.LogSettings</ReferencedTable><ReferencedColumn>LogSettingId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_NVRCamera_CameraId</FKConstraintName><FKTable>rs.NVRCameras</FKTable><FKColumn>CameraId</FKColumn><ReferencedTable>cam.Cameras</ReferencedTable><ReferencedColumn>CameraId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_NVRCamera_NVRId</FKConstraintName><FKTable>rs.NVRCameras</FKTable><FKColumn>NVRId</FKColumn><ReferencedTable>rs.NVRs</ReferencedTable><ReferencedColumn>NVRId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_NVR_RecordingServerId</FKConstraintName><FKTable>rs.NVRs</FKTable><FKColumn>RecordingServerId</FKColumn><ReferencedTable>rs.RecordingServers</ReferencedTable><ReferencedColumn>RecordingServerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_Nvr_VideoDeviceId</FKConstraintName><FKTable>rs.NVRs</FKTable><FKColumn>VideoDeviceId</FKColumn><ReferencedTable>rsd.VideoDevices</ReferencedTable><ReferencedColumn>VideoDeviceId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_PresetZones_RecordingServerId</FKConstraintName><FKTable>rs.PresetZones</FKTable><FKColumn>RecordingServerId</FKColumn><ReferencedTable>rs.RecordingServers</ReferencedTable><ReferencedColumn>RecordingServerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_RecordingServer_ConnectionId</FKConstraintName><FKTable>rs.RecordingServers</FKTable><FKColumn>ConnectionId</FKColumn><ReferencedTable>com.Connections</ReferencedTable><ReferencedColumn>ConnectionId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_RecordingServer_EventNotificationId</FKConstraintName><FKTable>rs.RecordingServers</FKTable><FKColumn>EventNotificationId</FKColumn><ReferencedTable>rs.EventNotifications</ReferencedTable><ReferencedColumn>EventNotificationId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_RecordingServer_LicenseId</FKConstraintName><FKTable>rs.RecordingServers</FKTable><FKColumn>LicenseId</FKColumn><ReferencedTable>lic.Licenses</ReferencedTable><ReferencedColumn>LicenseId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_RecordingServer_LocalSecureWebServerId</FKConstraintName><FKTable>rs.RecordingServers</FKTable><FKColumn>LocalSecureWebServerId</FKColumn><ReferencedTable>com.LocalSecureWebServers</ReferencedTable><ReferencedColumn>LocalSecureWebServerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_RecordingServer_LocalWebServerId</FKConstraintName><FKTable>rs.RecordingServers</FKTable><FKColumn>LocalWebServerId</FKColumn><ReferencedTable>com.LocalWebServers</ReferencedTable><ReferencedColumn>LocalWebServerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_RecordingServer_LogSettingId</FKConstraintName><FKTable>rs.RecordingServers</FKTable><FKColumn>LogSettingId</FKColumn><ReferencedTable>rs.LogSettings</ReferencedTable><ReferencedColumn>LogSettingId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_RecordingServer_PortId</FKConstraintName><FKTable>rs.RecordingServers</FKTable><FKColumn>PortId</FKColumn><ReferencedTable>com.Ports</ReferencedTable><ReferencedColumn>PortId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_RecordingServer_PrimaryRecordingServerFailoverConfigId</FKConstraintName><FKTable>rs.RecordingServers</FKTable><FKColumn>PrimaryRecordingServerFailoverConfigId</FKColumn><ReferencedTable>rsf.PrimaryRecordingServerFailoverConfigs</ReferencedTable><ReferencedColumn>PrimaryRecordingServerFailoverConfigId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_RecordingServer_StandbyRecordingServerFailoverConfigId</FKConstraintName><FKTable>rs.RecordingServers</FKTable><FKColumn>StandbyRecordingServerFailoverConfigId</FKColumn><ReferencedTable>rsf.StandbyRecordingServerFailoverConfigs</ReferencedTable><ReferencedColumn>StandbyRecordingServerFailoverConfigId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_RecordingServer_UdpBroadcastId</FKConstraintName><FKTable>rs.RecordingServers</FKTable><FKColumn>UdpBroadcastId</FKColumn><ReferencedTable>rs.UdpBroadcasts</ReferencedTable><ReferencedColumn>UdpBroadcastId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_VideoDecoder_RecordingServerId</FKConstraintName><FKTable>rs.VideoDecoders</FKTable><FKColumn>RecordingServerId</FKColumn><ReferencedTable>rs.RecordingServers</ReferencedTable><ReferencedColumn>RecordingServerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_VideoEncoder_RecordingServerId</FKConstraintName><FKTable>rs.VideoEncoders</FKTable><FKColumn>RecordingServerId</FKColumn><ReferencedTable>rs.RecordingServers</ReferencedTable><ReferencedColumn>RecordingServerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_VolumeExpirationId_VolumeId</FKConstraintName><FKTable>rs.VolumeExpirations</FKTable><FKColumn>VolumeId</FKColumn><ReferencedTable>rs.Volumes</ReferencedTable><ReferencedColumn>VolumeId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_VolumeExportId_VolumeId</FKConstraintName><FKTable>rs.VolumeExports</FKTable><FKColumn>VolumeId</FKColumn><ReferencedTable>rs.Volumes</ReferencedTable><ReferencedColumn>VolumeId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_VolumeRetentionId_ArchiveVolumeId</FKConstraintName><FKTable>rs.VolumeRetentions</FKTable><FKColumn>ArchiveVolumeId</FKColumn><ReferencedTable>rs.Volumes</ReferencedTable><ReferencedColumn>VolumeId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_VolumeRetentionId_BackupVolumeId</FKConstraintName><FKTable>rs.VolumeRetentions</FKTable><FKColumn>BackupVolumeId</FKColumn><ReferencedTable>rs.Volumes</ReferencedTable><ReferencedColumn>VolumeId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_VolumeRetentionId_VolumeId</FKConstraintName><FKTable>rs.VolumeRetentions</FKTable><FKColumn>VolumeId</FKColumn><ReferencedTable>rs.Volumes</ReferencedTable><ReferencedColumn>VolumeId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_Volume_RecordingServerId</FKConstraintName><FKTable>rs.Volumes</FKTable><FKColumn>RecordingServerId</FKColumn><ReferencedTable>rs.RecordingServers</ReferencedTable><ReferencedColumn>RecordingServerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_AudioDeviceServer_AudioDeviceId</FKConstraintName><FKTable>rsd.AudioDeviceServers</FKTable><FKColumn>AudioDeviceId</FKColumn><ReferencedTable>rsd.AudioDevices</ReferencedTable><ReferencedColumn>AudioDeviceId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_AudioDeviceServer_RecordingServerId</FKConstraintName><FKTable>rsd.AudioDeviceServers</FKTable><FKColumn>RecordingServerId</FKColumn><ReferencedTable>rs.RecordingServers</ReferencedTable><ReferencedColumn>RecordingServerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_CameraEvent_VideoDeviceId</FKConstraintName><FKTable>rsd.CameraEvents</FKTable><FKColumn>VideoDeviceId</FKColumn><ReferencedTable>rsd.VideoDevices</ReferencedTable><ReferencedColumn>VideoDeviceId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_DeviceTrigger_VideoDeviceId</FKConstraintName><FKTable>rsd.DeviceTriggers</FKTable><FKColumn>VideoDeviceId</FKColumn><ReferencedTable>rsd.VideoDevices</ReferencedTable><ReferencedColumn>VideoDeviceId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_DiskDeviceServer_DiskDeviceId</FKConstraintName><FKTable>rsd.DiskDeviceServers</FKTable><FKColumn>DiskDeviceId</FKColumn><ReferencedTable>rsd.DiskDevices</ReferencedTable><ReferencedColumn>DiskDeviceId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_DiskDeviceServer_RecordingServerId</FKConstraintName><FKTable>rsd.DiskDeviceServers</FKTable><FKColumn>RecordingServerId</FKColumn><ReferencedTable>rs.RecordingServers</ReferencedTable><ReferencedColumn>RecordingServerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_IODeviceServer_IODeviceId</FKConstraintName><FKTable>rsd.IODeviceServers</FKTable><FKColumn>IODeviceId</FKColumn><ReferencedTable>rsd.IODevices</ReferencedTable><ReferencedColumn>IODeviceId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_IODeviceServer_RecordingServerId</FKConstraintName><FKTable>rsd.IODeviceServers</FKTable><FKColumn>RecordingServerId</FKColumn><ReferencedTable>rs.RecordingServers</ReferencedTable><ReferencedColumn>RecordingServerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_MediaProfile_VideoDeviceId</FKConstraintName><FKTable>rsd.MediaProfiles</FKTable><FKColumn>VideoDeviceId</FKColumn><ReferencedTable>rsd.VideoDevices</ReferencedTable><ReferencedColumn>VideoDeviceId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_SerialDeviceServer_RecordingServerId</FKConstraintName><FKTable>rsd.SerialDeviceServers</FKTable><FKColumn>RecordingServerId</FKColumn><ReferencedTable>rs.RecordingServers</ReferencedTable><ReferencedColumn>RecordingServerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_SerialDeviceServer_SerialDeviceId</FKConstraintName><FKTable>rsd.SerialDeviceServers</FKTable><FKColumn>SerialDeviceId</FKColumn><ReferencedTable>rsd.SerialDevices</ReferencedTable><ReferencedColumn>SerialDeviceId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_VideoDevices_ProviderServerId</FKConstraintName><FKTable>rsd.VideoDevices</FKTable><FKColumn>ProviderServerId</FKColumn><ReferencedTable>rs.RecordingServers</ReferencedTable><ReferencedColumn>RecordingServerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_VideoDeviceServer_RecordingServerId</FKConstraintName><FKTable>rsd.VideoDeviceServers</FKTable><FKColumn>RecordingServerId</FKColumn><ReferencedTable>rs.RecordingServers</ReferencedTable><ReferencedColumn>RecordingServerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_VideoDeviceServer_VideoDeviceId</FKConstraintName><FKTable>rsd.VideoDeviceServers</FKTable><FKColumn>VideoDeviceId</FKColumn><ReferencedTable>rsd.VideoDevices</ReferencedTable><ReferencedColumn>VideoDeviceId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_PrimariesStandbyServers_PrimaryRecordingServerId</FKConstraintName><FKTable>rsf.PrimariesStandbyServers</FKTable><FKColumn>PrimaryRecordingServerId</FKColumn><ReferencedTable>rs.RecordingServers</ReferencedTable><ReferencedColumn>RecordingServerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_PrimariesStandbyServers_StandbyRecordingServerId</FKConstraintName><FKTable>rsf.PrimariesStandbyServers</FKTable><FKColumn>StandbyRecordingServerId</FKColumn><ReferencedTable>rs.RecordingServers</ReferencedTable><ReferencedColumn>RecordingServerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_StandbyRecordingServerFailoverConfigs_PrimaryRecordingServerId</FKConstraintName><FKTable>rsf.StandbyRecordingServerFailoverConfigs</FKTable><FKColumn>PrimaryRecordingServerId</FKColumn><ReferencedTable>rs.RecordingServers</ReferencedTable><ReferencedColumn>RecordingServerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_AgentVis_RecordingServerId</FKConstraintName><FKTable>rsi.AgentVis</FKTable><FKColumn>RecordingServerId</FKColumn><ReferencedTable>rs.RecordingServers</ReferencedTable><ReferencedColumn>RecordingServerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_AxisOneClicks_RecordingServerId</FKConstraintName><FKTable>rsi.AxisOneClicks</FKTable><FKColumn>RecordingServerId</FKColumn><ReferencedTable>rs.RecordingServers</ReferencedTable><ReferencedColumn>RecordingServerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_Bolds_RecordingServerId</FKConstraintName><FKTable>rsi.Bolds</FKTable><FKColumn>RecordingServerId</FKColumn><ReferencedTable>rs.RecordingServers</ReferencedTable><ReferencedColumn>RecordingServerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_BriefCams_RecordingServerId</FKConstraintName><FKTable>rsi.BriefCams</FKTable><FKColumn>RecordingServerId</FKColumn><ReferencedTable>rs.RecordingServers</ReferencedTable><ReferencedColumn>RecordingServerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_S2s_RecordingServerId</FKConstraintName><FKTable>rsi.S2s</FKTable><FKColumn>RecordingServerId</FKColumn><ReferencedTable>rs.RecordingServers</ReferencedTable><ReferencedColumn>RecordingServerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_SureViews_RecordingServerId</FKConstraintName><FKTable>rsi.SureViews</FKTable><FKColumn>RecordingServerId</FKColumn><ReferencedTable>rs.RecordingServers</ReferencedTable><ReferencedColumn>RecordingServerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_StorageDrive_RecordingServerId</FKConstraintName><FKTable>rsp.StorageDrives</FKTable><FKColumn>RecordingServerId</FKColumn><ReferencedTable>rs.RecordingServers</ReferencedTable><ReferencedColumn>RecordingServerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_StoragePool_RecordingServerId</FKConstraintName><FKTable>rsp.StoragePools</FKTable><FKColumn>RecordingServerId</FKColumn><ReferencedTable>rs.RecordingServers</ReferencedTable><ReferencedColumn>RecordingServerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_StoragePoolSettings_RecordingServerId</FKConstraintName><FKTable>rsp.StoragePoolSettings</FKTable><FKColumn>RecordingServerId</FKColumn><ReferencedTable>rs.RecordingServers</ReferencedTable><ReferencedColumn>RecordingServerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_CameraSchedule_CameraId</FKConstraintName><FKTable>shl.CameraSchedules</FKTable><FKColumn>CameraId</FKColumn><ReferencedTable>cam.Cameras</ReferencedTable><ReferencedColumn>CameraId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_CameraSchedule_ScheduleId</FKConstraintName><FKTable>shl.CameraSchedules</FKTable><FKColumn>ScheduleId</FKColumn><ReferencedTable>shl.Schedules</ReferencedTable><ReferencedColumn>ScheduleId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_EventTriggerSchedule_EventTriggerId</FKConstraintName><FKTable>shl.EventTriggerSchedules</FKTable><FKColumn>EventTriggerId</FKColumn><ReferencedTable>rs.EventTriggers</ReferencedTable><ReferencedColumn>EventTriggerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_EventTriggerSchedule_ScheduleId</FKConstraintName><FKTable>shl.EventTriggerSchedules</FKTable><FKColumn>ScheduleId</FKColumn><ReferencedTable>shl.Schedules</ReferencedTable><ReferencedColumn>ScheduleId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_ScheduledimeSegment_ScheduleId</FKConstraintName><FKTable>shl.ScheduledTimeSegments</FKTable><FKColumn>ScheduleId</FKColumn><ReferencedTable>shl.Schedules</ReferencedTable><ReferencedColumn>ScheduleId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_ScheduledimeSegment_ScheduleOperationId</FKConstraintName><FKTable>shl.ScheduledTimeSegments</FKTable><FKColumn>ScheduleOperationId</FKColumn><ReferencedTable>shl.ScheduleOperations</ReferencedTable><ReferencedColumn>ScheduleOperationId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_ScheduleOperation_ScheduleId</FKConstraintName><FKTable>shl.ScheduleOperations</FKTable><FKColumn>ScheduleId</FKColumn><ReferencedTable>shl.Schedules</ReferencedTable><ReferencedColumn>ScheduleId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_Schedule_RecordingServerId</FKConstraintName><FKTable>shl.Schedules</FKTable><FKColumn>RecordingServerId</FKColumn><ReferencedTable>rs.RecordingServers</ReferencedTable><ReferencedColumn>RecordingServerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_PasswordHistory_UserId</FKConstraintName><FKTable>usr.PasswordHistory</FKTable><FKColumn>UserId</FKColumn><ReferencedTable>usr.Users</ReferencedTable><ReferencedColumn>UserId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_UserCameras_CameraId</FKConstraintName><FKTable>usr.UserCameras</FKTable><FKColumn>CameraId</FKColumn><ReferencedTable>cam.Cameras</ReferencedTable><ReferencedColumn>CameraId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_UserCameras_ConfigurationId</FKConstraintName><FKTable>usr.UserCameras</FKTable><FKColumn>ConfigurationId</FKColumn><ReferencedTable>usr.Configurations</ReferencedTable><ReferencedColumn>ConfigurationId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_UserEvents_ConfigurationId</FKConstraintName><FKTable>usr.UserEvents</FKTable><FKColumn>ConfigurationId</FKColumn><ReferencedTable>usr.Configurations</ReferencedTable><ReferencedColumn>ConfigurationId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_UserEventTriggers_ConfigurationId</FKConstraintName><FKTable>usr.UserEventTriggers</FKTable><FKColumn>ConfigurationId</FKColumn><ReferencedTable>usr.Configurations</ReferencedTable><ReferencedColumn>ConfigurationId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_UserEventTriggers_EventTriggerId</FKConstraintName><FKTable>usr.UserEventTriggers</FKTable><FKColumn>EventTriggerId</FKColumn><ReferencedTable>rs.EventTriggers</ReferencedTable><ReferencedColumn>EventTriggerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_UserInputs_ConfigurationId</FKConstraintName><FKTable>usr.UserInputs</FKTable><FKColumn>ConfigurationId</FKColumn><ReferencedTable>usr.Configurations</ReferencedTable><ReferencedColumn>ConfigurationId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_UserInputs_IOInputId</FKConstraintName><FKTable>usr.UserInputs</FKTable><FKColumn>IOInputId</FKColumn><ReferencedTable>rs.IoInputs</ReferencedTable><ReferencedColumn>IoInputId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_UserModules_ConfigurationId</FKConstraintName><FKTable>usr.UserModules</FKTable><FKColumn>ConfigurationId</FKColumn><ReferencedTable>usr.Configurations</ReferencedTable><ReferencedColumn>ConfigurationId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_UserOutputs_ConfigurationId</FKConstraintName><FKTable>usr.UserOutputs</FKTable><FKColumn>ConfigurationId</FKColumn><ReferencedTable>usr.Configurations</ReferencedTable><ReferencedColumn>ConfigurationId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_UserOutputs_IOOutputId</FKConstraintName><FKTable>usr.UserOutputs</FKTable><FKColumn>IOOutputId</FKColumn><ReferencedTable>rs.IoOutputs</ReferencedTable><ReferencedColumn>IoOutputId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_Users_UserId</FKConstraintName><FKTable>usr.UserPasswordPolicies</FKTable><FKColumn>UserId</FKColumn><ReferencedTable>usr.Users</ReferencedTable><ReferencedColumn>UserId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_UserPresets_CameraId</FKConstraintName><FKTable>usr.UserPresets</FKTable><FKColumn>CameraId</FKColumn><ReferencedTable>cam.Cameras</ReferencedTable><ReferencedColumn>CameraId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_UserPresets_ConfigurationId</FKConstraintName><FKTable>usr.UserPresets</FKTable><FKColumn>ConfigurationId</FKColumn><ReferencedTable>usr.Configurations</ReferencedTable><ReferencedColumn>ConfigurationId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_UserRoles_ConfigurationId</FKConstraintName><FKTable>usr.UserRoles</FKTable><FKColumn>ConfigurationId</FKColumn><ReferencedTable>usr.Configurations</ReferencedTable><ReferencedColumn>ConfigurationId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_UserRoles_RoleId</FKConstraintName><FKTable>usr.UserRoles</FKTable><FKColumn>RoleId</FKColumn><ReferencedTable>usr.Roles</ReferencedTable><ReferencedColumn>RoleId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_Users_ActiveDirectoryId</FKConstraintName><FKTable>usr.Users</FKTable><FKColumn>ActiveDirectoryId</FKColumn><ReferencedTable>com.ActiveDirectories</ReferencedTable><ReferencedColumn>ActiveDirectoryId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_Users_ConfigurationId</FKConstraintName><FKTable>usr.Users</FKTable><FKColumn>ConfigurationId</FKColumn><ReferencedTable>usr.Configurations</ReferencedTable><ReferencedColumn>ConfigurationId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_UserServers_ConfigurationId</FKConstraintName><FKTable>usr.UserServers</FKTable><FKColumn>ConfigurationId</FKColumn><ReferencedTable>usr.Configurations</ReferencedTable><ReferencedColumn>ConfigurationId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_UserServers_RecordingServerId</FKConstraintName><FKTable>usr.UserServers</FKTable><FKColumn>RecordingServerId</FKColumn><ReferencedTable>rs.RecordingServers</ReferencedTable><ReferencedColumn>RecordingServerId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_UserViews_ConfigurationId</FKConstraintName><FKTable>usr.UserViews</FKTable><FKColumn>ConfigurationId</FKColumn><ReferencedTable>usr.Configurations</ReferencedTable><ReferencedColumn>ConfigurationId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_UserViews_ViewId</FKConstraintName><FKTable>usr.UserViews</FKTable><FKColumn>ViewId</FKColumn><ReferencedTable>vw.Views</ReferencedTable><ReferencedColumn>ViewId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_CameraView_CameraId</FKConstraintName><FKTable>vw.CameraViews</FKTable><FKColumn>CameraId</FKColumn><ReferencedTable>cam.Cameras</ReferencedTable><ReferencedColumn>CameraId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_CameraView_ViewId</FKConstraintName><FKTable>vw.CameraViews</FKTable><FKColumn>ViewId</FKColumn><ReferencedTable>vw.Views</ReferencedTable><ReferencedColumn>ViewId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_CameraViewTrigger_CameraViewId</FKConstraintName><FKTable>vw.CameraViewTriggers</FKTable><FKColumn>CameraViewId</FKColumn><ReferencedTable>vw.CameraViews</ReferencedTable><ReferencedColumn>CameraViewId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_CameraViewTrigger_IoOutputId</FKConstraintName><FKTable>vw.CameraViewTriggers</FKTable><FKColumn>IoOutputId</FKColumn><ReferencedTable>rs.IoOutputs</ReferencedTable><ReferencedColumn>IoOutputId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_CameraViewTrigger_ViewId</FKConstraintName><FKTable>vw.CameraViewTriggers</FKTable><FKColumn>ViewId</FKColumn><ReferencedTable>vw.Views</ReferencedTable><ReferencedColumn>ViewId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_FixedTile_TileId</FKConstraintName><FKTable>vw.FixedTiles</FKTable><FKColumn>TileId</FKColumn><ReferencedTable>vw.Tiles</ReferencedTable><ReferencedColumn>TileId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_FixedTile_ViewId</FKConstraintName><FKTable>vw.FixedTiles</FKTable><FKColumn>ViewId</FKColumn><ReferencedTable>vw.Views</ReferencedTable><ReferencedColumn>ViewId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_Map_MapImageId</FKConstraintName><FKTable>vw.Maps</FKTable><FKColumn>MapImageId</FKColumn><ReferencedTable>vw.MapImages</ReferencedTable><ReferencedColumn>MapImageId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_MapViewCamera_CameraId</FKConstraintName><FKTable>vw.MapViewCameras</FKTable><FKColumn>CameraId</FKColumn><ReferencedTable>cam.Cameras</ReferencedTable><ReferencedColumn>CameraId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_MapViewCamera_MapViewId</FKConstraintName><FKTable>vw.MapViewCameras</FKTable><FKColumn>MapViewId</FKColumn><ReferencedTable>vw.MapViews</ReferencedTable><ReferencedColumn>MapViewId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_MapViewCamera_ViewId</FKConstraintName><FKTable>vw.MapViewCameras</FKTable><FKColumn>ViewId</FKColumn><ReferencedTable>vw.Views</ReferencedTable><ReferencedColumn>ViewId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_MapViewLink_LinkId</FKConstraintName><FKTable>vw.MapViewLinks</FKTable><FKColumn>LinkedViewId</FKColumn><ReferencedTable>vw.Views</ReferencedTable><ReferencedColumn>ViewId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_MapViewLink_MapViewId</FKConstraintName><FKTable>vw.MapViewLinks</FKTable><FKColumn>MapViewId</FKColumn><ReferencedTable>vw.MapViews</ReferencedTable><ReferencedColumn>MapViewId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_MapViewLink_ViewId</FKConstraintName><FKTable>vw.MapViewLinks</FKTable><FKColumn>ViewId</FKColumn><ReferencedTable>vw.Views</ReferencedTable><ReferencedColumn>ViewId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_MapView_MapId</FKConstraintName><FKTable>vw.MapViews</FKTable><FKColumn>MapId</FKColumn><ReferencedTable>vw.Maps</ReferencedTable><ReferencedColumn>MapId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_MapView_ViewId</FKConstraintName><FKTable>vw.MapViews</FKTable><FKColumn>ViewId</FKColumn><ReferencedTable>vw.Views</ReferencedTable><ReferencedColumn>ViewId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_Pin_MapViewId</FKConstraintName><FKTable>vw.Pins</FKTable><FKColumn>MapViewId</FKColumn><ReferencedTable>vw.MapViews</ReferencedTable><ReferencedColumn>MapViewId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_Pin_ViewId</FKConstraintName><FKTable>vw.Pins</FKTable><FKColumn>ViewId</FKColumn><ReferencedTable>vw.Views</ReferencedTable><ReferencedColumn>ViewId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_ResponsiveTile_ParentTileId</FKConstraintName><FKTable>vw.ResponsiveTiles</FKTable><FKColumn>ParentTileId</FKColumn><ReferencedTable>vw.ResponsiveTiles</ReferencedTable><ReferencedColumn>ResponsiveTileId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_ResponsiveTile_TileId</FKConstraintName><FKTable>vw.ResponsiveTiles</FKTable><FKColumn>TileId</FKColumn><ReferencedTable>vw.Tiles</ReferencedTable><ReferencedColumn>TileId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_ResponsiveTile_ViewId</FKConstraintName><FKTable>vw.ResponsiveTiles</FKTable><FKColumn>ViewId</FKColumn><ReferencedTable>vw.Views</ReferencedTable><ReferencedColumn>ViewId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_Tile_CameraViewId</FKConstraintName><FKTable>vw.Tiles</FKTable><FKColumn>CameraViewId</FKColumn><ReferencedTable>vw.CameraViews</ReferencedTable><ReferencedColumn>CameraViewId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_Tile_MapViewId</FKConstraintName><FKTable>vw.Tiles</FKTable><FKColumn>MapViewId</FKColumn><ReferencedTable>vw.MapViews</ReferencedTable><ReferencedColumn>MapViewId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_Tile_WallViewId</FKConstraintName><FKTable>vw.Tiles</FKTable><FKColumn>WallViewId</FKColumn><ReferencedTable>vw.WallViews</ReferencedTable><ReferencedColumn>WallViewId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_Tile_WebViewId</FKConstraintName><FKTable>vw.Tiles</FKTable><FKColumn>WebViewId</FKColumn><ReferencedTable>vw.WebViews</ReferencedTable><ReferencedColumn>WebViewId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_View_CreatedByUserId</FKConstraintName><FKTable>vw.Views</FKTable><FKColumn>CreatedByUserId</FKColumn><ReferencedTable>usr.Users</ReferencedTable><ReferencedColumn>UserId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_View_RelatedViewId</FKConstraintName><FKTable>vw.Views</FKTable><FKColumn>RelatedViewId</FKColumn><ReferencedTable>vw.Views</ReferencedTable><ReferencedColumn>ViewId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_WallView_ViewId</FKConstraintName><FKTable>vw.WallViews</FKTable><FKColumn>ViewId</FKColumn><ReferencedTable>vw.Views</ReferencedTable><ReferencedColumn>ViewId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_WallViews_WvAgentDisplayId</FKConstraintName><FKTable>vw.WallViews</FKTable><FKColumn>WvAgentDisplayId</FKColumn><ReferencedTable>wall.WvAgentDisplays</ReferencedTable><ReferencedColumn>WvAgentDisplayId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_WebView_ViewId</FKConstraintName><FKTable>vw.WebViews</FKTable><FKColumn>ViewId</FKColumn><ReferencedTable>vw.Views</ReferencedTable><ReferencedColumn>ViewId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_TemplateTile_ParentTemplateTileId</FKConstraintName><FKTable>vwt.TemplateTiles</FKTable><FKColumn>ParentTemplateTileId</FKColumn><ReferencedTable>vwt.TemplateTiles</ReferencedTable><ReferencedColumn>TemplateTileId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_TemplateTile_ViewTemplateId</FKConstraintName><FKTable>vwt.TemplateTiles</FKTable><FKColumn>ViewTemplateId</FKColumn><ReferencedTable>vwt.ViewTemplates</ReferencedTable><ReferencedColumn>ViewTemplateId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item><Item><FKConstraintName>FK_WvAgentDisplays_WallViewAgentId</FKConstraintName><FKTable>wall.WvAgentDisplays</FKTable><FKColumn>WallViewAgentId</FKColumn><ReferencedTable>wall.WallViewAgents</ReferencedTable><ReferencedColumn>WallViewAgentId</ReferencedColumn><DeleteAction>NO_ACTION</DeleteAction><UpdateAction>NO_ACTION</UpdateAction></Item></EnhancedForeignKeyRelationships><EnhancedIndexInformation /><CheckConstraints /><ViewsandDefinitions /><StoredProceduresandFunctions /><Triggers /><UserDefinedDataTypes /><ExtendedProperties /><TableStatistics /><ObjectDependencies><Item><ReferencingObject>cfg.RegionMembers</ReferencingObject><ReferencingType>USER_TABLE</ReferencingType><ReferencedObject>cfg.RegionMembers</ReferencedObject><ReferencedType>USER_TABLE</ReferencedType></Item><Item><ReferencingObject>usr.Users</ReferencingObject><ReferencingType>USER_TABLE</ReferencingType><ReferencedObject>usr.Users</ReferencedObject><ReferencedType>USER_TABLE</ReferencedType></Item></ObjectDependencies><DatabaseInformation><Item><DatabaseName>CompleteViewVms</DatabaseName><SQLServerVersion>15.0.2130.3</SQLServerVersion><SQLServerEdition>Express Edition (64-bit)</SQLServerEdition><ServerCollation>SQL_Latin1_General_CP1_CI_AS</ServerCollation><DatabaseCollation>SQL_Latin1_General_CP1_CI_AS</DatabaseCollation><DatabaseCreated>2023-12-18 17:32:37.547000</DatabaseCreated><CompatibilityLevel>150</CompatibilityLevel></Item></DatabaseInformation></SchemaInformation></DatabaseSchema>