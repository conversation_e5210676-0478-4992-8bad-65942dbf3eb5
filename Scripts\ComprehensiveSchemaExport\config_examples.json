{"default_config": {"description": "Default configuration with system schema exclusion", "server": "your-server\\instance", "database": "your-database", "username": "your-username", "password": "your-password", "output_formats": ["json", "xml", "txt"], "sample_size": 10, "max_distinct_values": 50, "backup_to_desktop": true, "excluded_schemas": ["sys", "INFORMATION_SCHEMA", "guest", "db_owner", "db_accessadmin", "db_<PERSON><PERSON><PERSON>", "db_ddl<PERSON>min", "db_backupoperator", "db_datareader", "db_datawriter", "db_denydatareader", "db_denydatawriter"], "schema_filter_mode": "exclude"}, "enterprise_multi_vendor": {"description": "Enterprise environment with multiple vendors - exclude common vendor schemas", "excluded_schemas": ["sys", "INFORMATION_SCHEMA", "guest", "db_owner", "db_accessadmin", "db_<PERSON><PERSON><PERSON>", "db_ddl<PERSON>min", "db_backupoperator", "db_datareader", "db_datawriter", "db_denydatareader", "db_denydatawriter", "SSISDB", "ReportServer", "ReportServerTempDB", "msdb", "model", "tempdb", "master", "aspnet_regsql", "aspnet_membership", "aspnet_profile", "HangFire", "<PERSON><PERSON>", "NLog", "Serilog", "IdentityServer", "OpenIddict", "Auth0", "Telerik", "DevExpress", "Syncfusion", "backup", "temp", "staging", "etl", "import"], "schema_filter_mode": "exclude"}, "application_only": {"description": "Include only specific application schemas", "included_schemas": ["dbo", "app", "business", "reporting"], "schema_filter_mode": "include"}, "completeview_focused": {"description": "CompleteView specific - exclude system and focus on application schemas", "excluded_schemas": ["sys", "INFORMATION_SCHEMA", "guest", "db_owner", "db_accessadmin", "db_<PERSON><PERSON><PERSON>", "db_ddl<PERSON>min", "db_backupoperator", "db_datareader", "db_datawriter", "backup", "temp", "staging", "archive", "audit_old"], "included_schemas": ["dbo", "rs", "com", "cfg", "log"], "schema_filter_mode": "include"}, "ai_optimized": {"description": "Optimized for AI consumption with more data samples", "output_formats": ["json"], "sample_size": 25, "max_distinct_values": 100, "backup_to_desktop": true, "excluded_schemas": ["sys", "INFORMATION_SCHEMA", "guest", "backup", "temp", "staging", "archive"], "schema_filter_mode": "exclude"}, "development_all_schemas": {"description": "Development environment - include everything for analysis", "schema_filter_mode": "all", "sample_size": 5, "max_distinct_values": 25}, "production_safe": {"description": "Production environment - minimal impact with focused export", "output_formats": ["json"], "sample_size": 3, "max_distinct_values": 10, "backup_to_desktop": false, "connection_timeout": 60, "command_timeout": 600, "excluded_schemas": ["sys", "INFORMATION_SCHEMA", "guest", "backup", "temp", "staging", "archive", "audit", "monitoring", "logging", "etl", "import"], "schema_filter_mode": "exclude"}}