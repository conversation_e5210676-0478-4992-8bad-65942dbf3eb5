================================================================================
COMPREHENSIVE DATABASE SCHEMA EXPORT
================================================================================

EXPORT METADATA:
----------------------------------------
Export Timestamp: 2025-06-30T17:10:17.433729
Database Server: REMD-ZS-JV6GJV2\SQLEXPRESS
Database Name: CompleteViewVms
Export Version: 2.1
Total Tables Found: 20
Tables Sampled: 20
Max Table Rows For Sampling: 1000000
Max Table Rows For Stats: 10000000
Schema Filter Mode: exclude
Ai Features Enabled: {'system_stats': True, 'column_patterns': True, 'relationship_detection': True, 'ai_hints': True, 'query_examples': True}


ENHANCED TABLES AND COLUMNS:
---------------------------
  FullTableName: cam.AdvancedSettings
  COLUMN_NAME: AdvancedSettingId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: cam.AdvancedSettings
  COLUMN_NAME: VideoEncoderType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.AdvancedSettings
  COLUMN_NAME: VideoDecoderType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.Camera360Lens
  COLUMN_NAME: Camera360LensId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: cam.Camera360Lens
  COLUMN_NAME: DewarpLensType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.Camera360Lens
  COLUMN_NAME: DewarpLensOrientationType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.Camera360Lens
  COLUMN_NAME: DewarpImmervisionLensProfileType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.Camera360Lens
  COLUMN_NAME: DewarpViewType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.Camera360Lens
  COLUMN_NAME: FishEyeCenterX
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.Camera360Lens
  COLUMN_NAME: FishEyeCenterY
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.Camera360Lens
  COLUMN_NAME: FishEyeRadius
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.Cameras
  COLUMN_NAME: CameraId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: cam.Cameras
  COLUMN_NAME: CameraGuid
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.Cameras
  COLUMN_NAME: RecordingServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: cam.Cameras
  COLUMN_NAME: VideoDeviceId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: cam.Cameras
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.Cameras
  COLUMN_NAME: Name
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 300
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.Cameras
  COLUMN_NAME: TimeZone
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 40
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.Cameras
  COLUMN_NAME: CameraNo
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.Cameras
  COLUMN_NAME: ChannelNo
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.Cameras
  COLUMN_NAME: Latitude
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 20
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.Cameras
  COLUMN_NAME: Longitude
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 20
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 11
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.Cameras
  COLUMN_NAME: AlarmPresetType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 12
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.Cameras
  COLUMN_NAME: MotionSensitivity
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 13
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.Cameras
  COLUMN_NAME: AdvancedSettingId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 14
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: cam.Cameras
  COLUMN_NAME: EdgeSettingId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 15
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: cam.Cameras
  COLUMN_NAME: RecordingSettingId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 16
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: cam.Cameras
  COLUMN_NAME: Camera360LensId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 17
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: cam.Cameras
  COLUMN_NAME: IpSettingId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 18
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: cam.Cameras
  COLUMN_NAME: StretchSettingId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 19
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: cam.Cameras
  COLUMN_NAME: PtzSettingId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 20
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: cam.Cameras
  COLUMN_NAME: StreamProcessingId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 21
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: cam.Cameras
  COLUMN_NAME: ConeId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 22
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: cam.Cameras
  COLUMN_NAME: Note
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 23
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.CameraVolumes
  COLUMN_NAME: CameraVolumeId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: cam.CameraVolumes
  COLUMN_NAME: CameraId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: cam.CameraVolumes
  COLUMN_NAME: VolumeId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: cam.CamEvents
  COLUMN_NAME: CamEventId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: cam.CamEvents
  COLUMN_NAME: CameraId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: cam.CamEvents
  COLUMN_NAME: CameraEventId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: cam.CamEvents
  COLUMN_NAME: OnEventType
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.CamEvents
  COLUMN_NAME: OffEventType
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.CamEvents
  COLUMN_NAME: EventAlias
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.CamEvents
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: ((1))
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.Cones
  COLUMN_NAME: ConeId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: cam.Cones
  COLUMN_NAME: RotateAngle
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.Cones
  COLUMN_NAME: WidthAngle
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.Cones
  COLUMN_NAME: Depth
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.EdgeSettings
  COLUMN_NAME: EdgeSettingId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: cam.EdgeSettings
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.EdgeSettings
  COLUMN_NAME: EdgeStorageType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.EdgeSettings
  COLUMN_NAME: WillFormatSdCard
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.EdgeSettings
  COLUMN_NAME: NasAddress
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.EdgeSettings
  COLUMN_NAME: NasPath
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 1024
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.EdgeSettings
  COLUMN_NAME: NasUserName
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 64
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.EdgeSettings
  COLUMN_NAME: NasPassword
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.EdgeSettings
  COLUMN_NAME: SyncStartTime
  DATA_TYPE: datetime
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.EdgeSettings
  COLUMN_NAME: SyncInterval
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.EdgeSettings
  COLUMN_NAME: WillSyncServerOffline
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 11
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.IpSettings
  COLUMN_NAME: IpSettingId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: cam.IpSettings
  COLUMN_NAME: IpAddress
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.IpSettings
  COLUMN_NAME: UserName
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 40
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.IpSettings
  COLUMN_NAME: Password
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.IpSettings
  COLUMN_NAME: Manufacturer
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 80
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.IpSettings
  COLUMN_NAME: Model
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.IpSettings
  COLUMN_NAME: ProtocolType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.IpSettings
  COLUMN_NAME: RttpPort
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.IpSettings
  COLUMN_NAME: ControlPort
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.IpSettings
  COLUMN_NAME: StreamPort
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.IpSettings
  COLUMN_NAME: RtspTcpPort
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 11
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.IpSettings
  COLUMN_NAME: RtspUdpPort
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 12
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.IpSettings
  COLUMN_NAME: HttpPort
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 13
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.IpSettings
  COLUMN_NAME: Path
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 2048
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 14
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.IpSettings
  COLUMN_NAME: Timeout
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 15
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.IpSettings
  COLUMN_NAME: Retries
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 16
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.IpSettings
  COLUMN_NAME: UseAxisProxy
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 17
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.IpSettings
  COLUMN_NAME: KeepAliveMethodType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 18
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.IpSettings
  COLUMN_NAME: UseAxisStreamProfile
  DATA_TYPE: bit
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 19
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.IpSettings
  COLUMN_NAME: AxisStreamProfile
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 20
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.MediaStreams
  COLUMN_NAME: MediaStreamId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: cam.MediaStreams
  COLUMN_NAME: CameraId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: cam.MediaStreams
  COLUMN_NAME: StreamType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.MediaStreams
  COLUMN_NAME: StreamName
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 64
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.MediaStreams
  COLUMN_NAME: StreamTokenName
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 64
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.MediaStreams
  COLUMN_NAME: StreamNo
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.MediaStreams
  COLUMN_NAME: MultiStreamNo
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.MediaStreams
  COLUMN_NAME: VideoSourceFormatType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.MediaStreams
  COLUMN_NAME: VideoSourceFormatProfileType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.MediaStreams
  COLUMN_NAME: VideoWidth
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.MediaStreams
  COLUMN_NAME: VideoHeight
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 11
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.MediaStreams
  COLUMN_NAME: VideoResolution
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 12
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.MediaStreams
  COLUMN_NAME: VideoQuality
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 13
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.MediaStreams
  COLUMN_NAME: VideoFps
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 14
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.MediaStreams
  COLUMN_NAME: VideoGov
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 15
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.MediaStreams
  COLUMN_NAME: VideoBitrateControlType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 16
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.MediaStreams
  COLUMN_NAME: VideoAverageBitrate
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 17
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.MediaStreams
  COLUMN_NAME: VideoMaximumBitrate
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 18
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.MediaStreams
  COLUMN_NAME: IsAudioEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 19
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.MediaStreams
  COLUMN_NAME: AudioDeviceId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 20
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: cam.MediaStreams
  COLUMN_NAME: AudioChannelNo
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 21
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.MediaStreams
  COLUMN_NAME: VideoRecordFormatType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 22
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.MediaStreams
  COLUMN_NAME: VideoLiveFormatType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 23
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.MediaStreams
  COLUMN_NAME: IsScheduledRecordingEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 24
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.MediaStreams
  COLUMN_NAME: IsAlarmRecordingEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 25
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.MediaStreams
  COLUMN_NAME: IsMotionRecordingEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 26
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.MediaStreams
  COLUMN_NAME: IsOnDemandCamera
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 27
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.MediaStreams
  COLUMN_NAME: OnDemandDisconnectTime
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 28
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.MediaStreams
  COLUMN_NAME: RecompressionOn
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 29
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.MotionZones
  COLUMN_NAME: MotionZoneId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: cam.MotionZones
  COLUMN_NAME: CameraId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: cam.MotionZones
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.MotionZones
  COLUMN_NAME: ZoneIndex
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.MotionZones
  COLUMN_NAME: Name
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 64
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.MotionZones
  COLUMN_NAME: PointX
  DATA_TYPE: float
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 53
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.MotionZones
  COLUMN_NAME: PointY
  DATA_TYPE: float
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 53
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.MotionZones
  COLUMN_NAME: Width
  DATA_TYPE: float
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 53
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.MotionZones
  COLUMN_NAME: Height
  DATA_TYPE: float
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 53
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.MotionZones
  COLUMN_NAME: Priority
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.MotionZones
  COLUMN_NAME: DoRecSchedule
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 11
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.Presets
  COLUMN_NAME: PresetId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: cam.Presets
  COLUMN_NAME: PtzSettingId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: cam.Presets
  COLUMN_NAME: Name
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 32
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.Presets
  COLUMN_NAME: PresetIndex
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.Presets
  COLUMN_NAME: DwellTimeSeconds
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.Presets
  COLUMN_NAME: DoIncludeTour
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.ProCamps
  COLUMN_NAME: ProCampId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: cam.ProCamps
  COLUMN_NAME: MediaStreamId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: cam.ProCamps
  COLUMN_NAME: PropertyType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.ProCamps
  COLUMN_NAME: Value
  DATA_TYPE: bigint
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 19
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.ProCamps
  COLUMN_NAME: Flags
  DATA_TYPE: bigint
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 19
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.PtzSettings
  COLUMN_NAME: PtzSettingId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: cam.PtzSettings
  COLUMN_NAME: IsPtzEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.PtzSettings
  COLUMN_NAME: ChannelNo
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.PtzSettings
  COLUMN_NAME: PtzDriver
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.PtzSettings
  COLUMN_NAME: SerialPort
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.PtzSettings
  COLUMN_NAME: DoFlipDirections
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.PtzSettings
  COLUMN_NAME: TourResumeTime
  DATA_TYPE: time
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.PtzSettings
  COLUMN_NAME: InactivityTimeoutInterval
  DATA_TYPE: time
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.PtzSettings
  COLUMN_NAME: DoAutoHome
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.PtzSettings
  COLUMN_NAME: DoPresetTour
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.PtzSettings
  COLUMN_NAME: DoTourOffOnStart
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 11
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.PtzSettings
  COLUMN_NAME: DoPersistConnection
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 12
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.PtzSettings
  COLUMN_NAME: UseAnalogPtz
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 13
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.PtzSettings
  COLUMN_NAME: HomePreset1
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 14
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.PtzSettings
  COLUMN_NAME: HomePreset2
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 15
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.PtzSettings
  COLUMN_NAME: HomePreset3
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 16
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.PtzSettings
  COLUMN_NAME: HomePreset4
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 17
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.RecordingSettings
  COLUMN_NAME: RecordingSettingId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: cam.RecordingSettings
  COLUMN_NAME: StorageType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.RecordingSettings
  COLUMN_NAME: SchedFps
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.RecordingSettings
  COLUMN_NAME: MotionFps
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.RecordingSettings
  COLUMN_NAME: AlarmFps
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.RecordingSettings
  COLUMN_NAME: PreMotionSeconds
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.RecordingSettings
  COLUMN_NAME: PostMotionSeconds
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.RecordingSettings
  COLUMN_NAME: PreAlarmSecords
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.RecordingSettings
  COLUMN_NAME: PostAlarmSeconds
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.RetentionPolicies
  COLUMN_NAME: RetentionPolicyId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: cam.RetentionPolicies
  COLUMN_NAME: CameraId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: cam.RetentionPolicies
  COLUMN_NAME: PoolType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.RetentionPolicies
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.RetentionPolicies
  COLUMN_NAME: ActionAfterDays
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.RetentionPolicies
  COLUMN_NAME: MinRetentionDays
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.RetentionPolicies
  COLUMN_NAME: MaxRetentionDays
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.RetentionPolicies
  COLUMN_NAME: StartTime
  DATA_TYPE: time
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.RetentionPolicies
  COLUMN_NAME: ContinuousClips
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.RetentionPolicies
  COLUMN_NAME: MotionClips
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.RetentionPolicies
  COLUMN_NAME: AlarmClips
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 11
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.StreamProcessings
  COLUMN_NAME: StreamProcessingId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: cam.StreamProcessings
  COLUMN_NAME: ExternalCameraNo
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.StreamProcessings
  COLUMN_NAME: IsStreamProcessingEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.StreamProcessings
  COLUMN_NAME: IsDftEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.StreamProcessings
  COLUMN_NAME: IsDvdEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.StreamProcessings
  COLUMN_NAME: WillFavorContinuousVideo
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.StreamProcessings
  COLUMN_NAME: DoVideoAnalytics
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.StreamProcessings
  COLUMN_NAME: LatencyDft
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.StreamProcessings
  COLUMN_NAME: GopCountDft
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.StretchSettings
  COLUMN_NAME: StretchSettingId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: cam.StretchSettings
  COLUMN_NAME: IsDeinterlacingEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.StretchSettings
  COLUMN_NAME: IsMotionBasedDeinterlacingEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.StretchSettings
  COLUMN_NAME: MotionDeinterlacingThreshold
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.StretchSettings
  COLUMN_NAME: IsMedianFilteringEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.StretchSettings
  COLUMN_NAME: IsNoiseReductionEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.StretchSettings
  COLUMN_NAME: NoiseReductionLumaStrength
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.StretchSettings
  COLUMN_NAME: NoiseReductionChromaStrength
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.VideoOverlays
  COLUMN_NAME: VideoOverlayId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: cam.VideoOverlays
  COLUMN_NAME: StreamProcessingId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: cam.VideoOverlays
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.VideoOverlays
  COLUMN_NAME: Name
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 64
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.VideoOverlays
  COLUMN_NAME: VideoOverlayType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cam.VideoOverlays
  COLUMN_NAME: VideoOverlayPositionType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cfg.RegionMembers
  COLUMN_NAME: RegionMemberId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: cfg.RegionMembers
  COLUMN_NAME: RegionHierarchyId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: cfg.RegionMembers
  COLUMN_NAME: RecordingServerId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: cfg.RegionMembers
  COLUMN_NAME: ViewId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: cfg.RegionsHierarchy
  COLUMN_NAME: RegionHierarchyId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: cfg.RegionsHierarchy
  COLUMN_NAME: RegionGuid
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cfg.RegionsHierarchy
  COLUMN_NAME: ParentId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: cfg.RegionsHierarchy
  COLUMN_NAME: RegionTypeId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: cfg.RegionsHierarchy
  COLUMN_NAME: Name
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 500
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cfg.RegionTypes
  COLUMN_NAME: RegionTypeId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: cfg.RegionTypes
  COLUMN_NAME: Ordinal
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cfg.RegionTypes
  COLUMN_NAME: Name
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 64
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cld.Credentials
  COLUMN_NAME: Email
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cld.Credentials
  COLUMN_NAME: Password
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cld.Credentials
  COLUMN_NAME: Secret
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cld.Service
  COLUMN_NAME: HostAddress
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cld.Service
  COLUMN_NAME: Port
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cld.Service
  COLUMN_NAME: IsSecure
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cld.Service
  COLUMN_NAME: DefaultTimeout
  DATA_TYPE: time
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cld.VpnProfilePush
  COLUMN_NAME: VpnProfilePushId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: cld.VpnProfilePush
  COLUMN_NAME: ServerGuid
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cld.VpnProfilePush
  COLUMN_NAME: VpnAddress
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: com.ActiveDirectories
  COLUMN_NAME: ActiveDirectoryId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: com.ActiveDirectories
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: com.ActiveDirectories
  COLUMN_NAME: Domain
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: com.ActiveDirectories
  COLUMN_NAME: BaseDn
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: com.ActiveDirectories
  COLUMN_NAME: Username
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 40
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: com.ActiveDirectories
  COLUMN_NAME: Password
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: com.ActiveDirectories
  COLUMN_NAME: SearchNestedDomains
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: com.ActiveDirectories
  COLUMN_NAME: SearchNestedGroups
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: com.ActiveDirectories
  COLUMN_NAME: GroupReauthIntervalSecs
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: com.Certificates
  COLUMN_NAME: CertificateId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: com.Certificates
  COLUMN_NAME: LocalSecureWebServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: com.Certificates
  COLUMN_NAME: Name
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: com.Connections
  COLUMN_NAME: ConnectionId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: com.Connections
  COLUMN_NAME: HostAddress
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: com.Connections
  COLUMN_NAME: Username
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 40
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: com.Connections
  COLUMN_NAME: Password
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: com.Connections
  COLUMN_NAME: Driver
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: com.Connections
  COLUMN_NAME: Retries
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: com.EmailServers
  COLUMN_NAME: EmailServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: com.EmailServers
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: com.EmailServers
  COLUMN_NAME: SmtpHostAddress
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: com.EmailServers
  COLUMN_NAME: SmtpHostPort
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: com.EmailServers
  COLUMN_NAME: Username
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 40
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: com.EmailServers
  COLUMN_NAME: Password
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: com.EmailServers
  COLUMN_NAME: WillUseTls
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: com.EmailServers
  COLUMN_NAME: WillAuthenticate
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: com.EmailServers
  COLUMN_NAME: SenderName
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 60
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: com.EmailServers
  COLUMN_NAME: SenderEmail
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: com.LocalSecureWebServers
  COLUMN_NAME: LocalSecureWebServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: com.LocalSecureWebServers
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: com.LocalSecureWebServers
  COLUMN_NAME: Port
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: com.LocalSecureWebServers
  COLUMN_NAME: MinTlsVersion
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: com.LocalWebServers
  COLUMN_NAME: LocalWebServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: com.LocalWebServers
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: com.LocalWebServers
  COLUMN_NAME: Port
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: com.Ports
  COLUMN_NAME: PortId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: com.Ports
  COLUMN_NAME: DataPort
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: com.Ports
  COLUMN_NAME: SecureDataPort
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: com.Ports
  COLUMN_NAME: AdminPort
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: com.Ports
  COLUMN_NAME: UdpMediaPortMin
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: com.Ports
  COLUMN_NAME: UdpMediaPortMax
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: com.Ports
  COLUMN_NAME: RtspPortIsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: com.Ports
  COLUMN_NAME: RtspPort
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: com.Ports
  COLUMN_NAME: RtspUseBasicAuth
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: ((0))
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cs.CommonSettings
  COLUMN_NAME: CommonSettingId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cs.CommonSettings
  COLUMN_NAME: Settings
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: -1
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cs.LoggingComponents
  COLUMN_NAME: LoggingComponentType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cs.LoggingComponents
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cs.LoggingComponents
  COLUMN_NAME: Identifier
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 64
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cs.LoggingComponents
  COLUMN_NAME: Name
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cso.BandwidthControls
  COLUMN_NAME: BandwidthControlId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: cso.BandwidthControls
  COLUMN_NAME: RecordingServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: cso.BandwidthControls
  COLUMN_NAME: IsOnDemandEventsChecked
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: cso.BandwidthControls
  COLUMN_NAME: StatusPeriodMs
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: dbo.Migrations
  COLUMN_NAME: MigrationId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: dbo.Migrations
  COLUMN_NAME: SQL
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 4000
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: dbo.Migrations
  COLUMN_NAME: FileName
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 255
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: dbo.Migrations
  COLUMN_NAME: AppliedDate
  DATA_TYPE: datetime
  IS_NULLABLE: NO
  COLUMN_DEFAULT: (getdate())
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: dbo.Migrations
  COLUMN_NAME: SqlHash
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: grp.GroupMembers
  COLUMN_NAME: GroupMemberId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: grp.GroupMembers
  COLUMN_NAME: GroupId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: grp.GroupMembers
  COLUMN_NAME: UserId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: grp.Groups
  COLUMN_NAME: GroupId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: grp.Groups
  COLUMN_NAME: ConfigurationId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: grp.Groups
  COLUMN_NAME: ParentId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: grp.Groups
  COLUMN_NAME: ActiveDirectoryId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: grp.Groups
  COLUMN_NAME: SubDomain
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: grp.Groups
  COLUMN_NAME: GroupGuid
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: grp.Groups
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: grp.Groups
  COLUMN_NAME: IsAdmin
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: grp.Groups
  COLUMN_NAME: RemoteAccess
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: grp.Groups
  COLUMN_NAME: Name
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 500
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: grp.Groups
  COLUMN_NAME: PtzPriority
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 11
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: grp.Groups
  COLUMN_NAME: Note
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 12
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: grp.Groups
  COLUMN_NAME: MsOwnerGuid
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 13
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: lic.Licenses
  COLUMN_NAME: LicenseId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: lic.Licenses
  COLUMN_NAME: LicenseType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: lic.Licenses
  COLUMN_NAME: IpLicensesPurchased
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: lic.Licenses
  COLUMN_NAME: AnalogLicensesPurchased
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: lic.Licenses
  COLUMN_NAME: FeatureKeysJson
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 4000
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.BackupConfigs
  COLUMN_NAME: BackupConfigId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: ms.BackupConfigs
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.BackupConfigs
  COLUMN_NAME: IsDefault
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.BackupConfigs
  COLUMN_NAME: Name
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 64
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.BackupConfigs
  COLUMN_NAME: CronExpression
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.BackupConfigs
  COLUMN_NAME: BackupPath
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.BackupConfigs
  COLUMN_NAME: BackupTimestampFormat
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 32
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.BackupConfigs
  COLUMN_NAME: BackupOptions
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: -1
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.DicoveredServers
  COLUMN_NAME: DicoveredServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: ms.DicoveredServers
  COLUMN_NAME: IpAddress
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.DicoveredServers
  COLUMN_NAME: FriendlyName
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 80
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.DicoveredServers
  COLUMN_NAME: ServerGuid
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.DicoveredServers
  COLUMN_NAME: LastUpdated
  DATA_TYPE: datetime
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.FederatedChildren
  COLUMN_NAME: Guid
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: ms.FederatedChildren
  COLUMN_NAME: Name
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 100
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.FederatedChildren
  COLUMN_NAME: Version
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 64
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.FederatedChildren
  COLUMN_NAME: UseTls
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.FederatedChildren
  COLUMN_NAME: HostAddress
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.FederatedChildren
  COLUMN_NAME: Port
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.FederatedChildren
  COLUMN_NAME: Identity
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.FederatedChildren
  COLUMN_NAME: Password
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.FederatedChildren
  COLUMN_NAME: Note
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.FederatedChildren
  COLUMN_NAME: WriteToken
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.FederatedChildren
  COLUMN_NAME: RecordingServersCount
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: ((0))
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 11
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.FederatedChildren
  COLUMN_NAME: CameraLicensesPurchased
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: ((0))
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 12
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.FederatedChildren
  COLUMN_NAME: CameraLicensesUsed
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: ((0))
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 13
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.FederatedParent
  COLUMN_NAME: Guid
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: ms.FederatedParent
  COLUMN_NAME: Name
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 100
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.FederatedParent
  COLUMN_NAME: Version
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 64
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.FederatedParent
  COLUMN_NAME: UseTls
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.FederatedParent
  COLUMN_NAME: HostAddress
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.FederatedParent
  COLUMN_NAME: Port
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.FederatedParent
  COLUMN_NAME: Note
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.FederatedParent
  COLUMN_NAME: WriteToken
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.FederatedSiblings
  COLUMN_NAME: Guid
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: ms.FederatedSiblings
  COLUMN_NAME: Name
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 100
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.FederatedSiblings
  COLUMN_NAME: Version
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 64
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.FederatedSiblings
  COLUMN_NAME: UseTls
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.FederatedSiblings
  COLUMN_NAME: HostAddress
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.FederatedSiblings
  COLUMN_NAME: Port
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.FederationSettings
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.Identity
  COLUMN_NAME: Guid
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.Identity
  COLUMN_NAME: Name
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 100
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.Identity
  COLUMN_NAME: HostAddressesJson
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 2000
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.Identity
  COLUMN_NAME: BaseUrl
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportRecordingServerQueue
  COLUMN_NAME: ImportRecordingServerQueueId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportRecordingServerQueue
  COLUMN_NAME: FriendlyName
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 100
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportRecordingServerQueue
  COLUMN_NAME: HostAddress
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 100
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportRecordingServerQueue
  COLUMN_NAME: SvrCtrlPortNo
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportRecordingServerQueue
  COLUMN_NAME: RestPortNo
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportRecordingServerQueue
  COLUMN_NAME: UseTls
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportRecordingServerQueue
  COLUMN_NAME: Username
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 64
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportRecordingServerQueue
  COLUMN_NAME: Password
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportRecordingServerQueue
  COLUMN_NAME: AutoProvision
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportRecordingServerQueue
  COLUMN_NAME: ConnectionTimeoutMs
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportRecordingServerQueue
  COLUMN_NAME: AddUserId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 11
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportRecordingServerQueue
  COLUMN_NAME: EnqueuedAt
  DATA_TYPE: datetime
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 12
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportRecordingServerQueue
  COLUMN_NAME: RegionHierarchyId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 13
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportRecordingServerQueue
  COLUMN_NAME: PostActions
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 4000
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 14
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportRecordingServerStatus
  COLUMN_NAME: ImportRecordingServerStatusId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportRecordingServerStatus
  COLUMN_NAME: FriendlyName
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 100
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportRecordingServerStatus
  COLUMN_NAME: HostAddress
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 100
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportRecordingServerStatus
  COLUMN_NAME: SvrCtrlPortNo
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportRecordingServerStatus
  COLUMN_NAME: RestPortNo
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportRecordingServerStatus
  COLUMN_NAME: UseTls
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportRecordingServerStatus
  COLUMN_NAME: Username
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 64
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportRecordingServerStatus
  COLUMN_NAME: Password
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportRecordingServerStatus
  COLUMN_NAME: AutoProvision
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportRecordingServerStatus
  COLUMN_NAME: AddUserId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportRecordingServerStatus
  COLUMN_NAME: RegionHierarchyId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 11
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportRecordingServerStatus
  COLUMN_NAME: EnqueuedAt
  DATA_TYPE: datetime
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 12
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportRecordingServerStatus
  COLUMN_NAME: ImportStatus
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 13
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportRecordingServerStatus
  COLUMN_NAME: StartedAt
  DATA_TYPE: datetime
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 14
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportRecordingServerStatus
  COLUMN_NAME: EndedAt
  DATA_TYPE: datetime
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 15
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportRecordingServerStatus
  COLUMN_NAME: Elapsed
  DATA_TYPE: time
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 16
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportRecordingServerStatus
  COLUMN_NAME: ErrorMessage
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 17
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportRecordingServerStatus
  COLUMN_NAME: PostActions
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 4000
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 18
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportServerConfigQueue
  COLUMN_NAME: ImportServerConfigQueueGuid
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportServerConfigQueue
  COLUMN_NAME: AutoProvision
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportServerConfigQueue
  COLUMN_NAME: FriendlyName
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 100
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportServerConfigQueue
  COLUMN_NAME: HostAddress
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 100
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportServerConfigQueue
  COLUMN_NAME: ServerGuid
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportServerConfigQueue
  COLUMN_NAME: Version
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 16
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportServerConfigQueue
  COLUMN_NAME: UseTls
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportServerConfigQueue
  COLUMN_NAME: Username
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 64
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportServerConfigQueue
  COLUMN_NAME: Password
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportServerConfigQueue
  COLUMN_NAME: Platform
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 64
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportServerConfigQueue
  COLUMN_NAME: SerialNo
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 64
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 11
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportServerConfigQueue
  COLUMN_NAME: EnqueuedAt
  DATA_TYPE: datetime
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 12
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportServerConfigQueue
  COLUMN_NAME: ServerTimeZoneInfo
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 100
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 13
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportServerConfigQueue
  COLUMN_NAME: EnvironmentInfo
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: -1
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 14
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportServerConfigQueue
  COLUMN_NAME: ServerConfigRoot
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: -1
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 15
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportServerConfigQueue
  COLUMN_NAME: LicenseInfo
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 400
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 16
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportServerConfigStatus
  COLUMN_NAME: ImportServerConfigStatusGuid
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportServerConfigStatus
  COLUMN_NAME: HostAddress
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 100
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportServerConfigStatus
  COLUMN_NAME: ServerGuid
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportServerConfigStatus
  COLUMN_NAME: StartedAt
  DATA_TYPE: datetime
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportServerConfigStatus
  COLUMN_NAME: EndedAt
  DATA_TYPE: datetime
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportServerConfigStatus
  COLUMN_NAME: Elapsed
  DATA_TYPE: time
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportServerConfigStatus
  COLUMN_NAME: WasSuccessful
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.ImportServerConfigStatus
  COLUMN_NAME: Message
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 1024
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.NamedResources
  COLUMN_NAME: NamedResourceId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: ms.NamedResources
  COLUMN_NAME: ResourceType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.NamedResources
  COLUMN_NAME: Name
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 64
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.NamedResources
  COLUMN_NAME: Description
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.NamedResources
  COLUMN_NAME: HostAddress
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.NamedResources
  COLUMN_NAME: SecondaryAddress
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.PushServerStatus
  COLUMN_NAME: PushServerStatusId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: ms.PushServerStatus
  COLUMN_NAME: RecordingServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: ms.PushServerStatus
  COLUMN_NAME: PushToServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.PushServerStatus
  COLUMN_NAME: PushDevices
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.PushServerStatus
  COLUMN_NAME: PushUsers
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.PushServerStatus
  COLUMN_NAME: PushConfiguration
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.PushServerStatus
  COLUMN_NAME: CorsWhitelist
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: -1
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.PushServerStatus
  COLUMN_NAME: TimeStamp
  DATA_TYPE: datetime
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.PushServerStatus
  COLUMN_NAME: WasSuccessful
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.PushServerStatus
  COLUMN_NAME: ErrorMessage
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.PushServerStatus
  COLUMN_NAME: PushMediaEnc
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: ((0))
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 11
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.PushToServers
  COLUMN_NAME: PushToServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: ms.PushToServers
  COLUMN_NAME: RecordingServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: ms.PushToServers
  COLUMN_NAME: PushDevices
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.PushToServers
  COLUMN_NAME: PushUsers
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.PushToServers
  COLUMN_NAME: PushConfiguration
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.PushToServers
  COLUMN_NAME: CorsWhitelist
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: -1
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.PushToServers
  COLUMN_NAME: TimeStamp
  DATA_TYPE: datetime
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.PushToServers
  COLUMN_NAME: HttpPort
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.PushToServers
  COLUMN_NAME: SecureHttpPort
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.PushToServers
  COLUMN_NAME: Priority
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: ((0))
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.PushToServers
  COLUMN_NAME: PushMediaEnc
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: ((0))
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 11
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.VersionInfo
  COLUMN_NAME: VersionInfoId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: ms.VersionInfo
  COLUMN_NAME: Major
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.VersionInfo
  COLUMN_NAME: Minor
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.VersionInfo
  COLUMN_NAME: Build
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.VersionInfo
  COLUMN_NAME: Revision
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: ms.VersionInfo
  COLUMN_NAME: TimeStamp
  DATA_TYPE: datetime
  IS_NULLABLE: YES
  COLUMN_DEFAULT: (NULL)
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.DefaultBehaviors
  COLUMN_NAME: DefaultBehaviorId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rs.DefaultBehaviors
  COLUMN_NAME: RecordingServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.DefaultBehaviors
  COLUMN_NAME: PacketLossMaxAcceptPercent
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.DefaultBehaviors
  COLUMN_NAME: PacketLossReportIntervalSec
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.DefaultBehaviors
  COLUMN_NAME: IsCameraAccessEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventNotifications
  COLUMN_NAME: EventNotificationId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventNotifications
  COLUMN_NAME: EmailSubject
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventNotifications
  COLUMN_NAME: EmailRecipients
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: -1
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventNotifications
  COLUMN_NAME: SmsSubject
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventNotifications
  COLUMN_NAME: SmsRecipients
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: -1
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventNotifications
  COLUMN_NAME: SmsGateway
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventNotificationSettings
  COLUMN_NAME: EventNotificationSettingId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventNotificationSettings
  COLUMN_NAME: EventNotificationId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.EventNotificationSettings
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventNotificationSettings
  COLUMN_NAME: SourceType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventNotificationSettings
  COLUMN_NAME: EventType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventNotificationSettings
  COLUMN_NAME: WillIncludeSnapshot
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventNotificationSettings
  COLUMN_NAME: MinInterval
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventNotificationSettings
  COLUMN_NAME: ConfirmationWaitFor
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventNotificationSettings
  COLUMN_NAME: ConfirmationWaitEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventsToLog
  COLUMN_NAME: EventToLogId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventsToLog
  COLUMN_NAME: RecordingServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.EventsToLog
  COLUMN_NAME: EventType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventTriggerActions
  COLUMN_NAME: EventTriggerActionId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventTriggerActions
  COLUMN_NAME: EventTriggerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.EventTriggerActions
  COLUMN_NAME: EventTriggerActionGuid
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventTriggerActions
  COLUMN_NAME: ActionType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventTriggerActions
  COLUMN_NAME: ActionValue
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventTriggerActions
  COLUMN_NAME: PriorityType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventTriggerActions
  COLUMN_NAME: IsPrimaryAction
  DATA_TYPE: bit
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventTriggerActions
  COLUMN_NAME: CameraId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.EventTriggerActions
  COLUMN_NAME: IOTriggerId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.EventTriggerActions
  COLUMN_NAME: VolumeId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.EventTriggerResponseActions
  COLUMN_NAME: EventTriggerResponseActionId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventTriggerResponseActions
  COLUMN_NAME: EventTriggerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.EventTriggerResponseActions
  COLUMN_NAME: EventTriggerResponseId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.EventTriggerResponseActions
  COLUMN_NAME: EventTriggerActionId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.EventTriggerResponses
  COLUMN_NAME: EventTriggerResponseId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventTriggerResponses
  COLUMN_NAME: EventTriggerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventTriggerResponses
  COLUMN_NAME: IsAckRequired
  DATA_TYPE: bit
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventTriggerResponses
  COLUMN_NAME: IsResetRequired
  DATA_TYPE: bit
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventTriggerResponses
  COLUMN_NAME: CanForward
  DATA_TYPE: bit
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventTriggers
  COLUMN_NAME: EventTriggerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventTriggers
  COLUMN_NAME: RecordingServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.EventTriggers
  COLUMN_NAME: EventTriggerResponseId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.EventTriggers
  COLUMN_NAME: EventTriggerGuid
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventTriggers
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventTriggers
  COLUMN_NAME: Name
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventTriggers
  COLUMN_NAME: SetIOTrigger
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventTriggers
  COLUMN_NAME: PriorityType
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventTriggers
  COLUMN_NAME: Note
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventTriggerSources
  COLUMN_NAME: EventTriggerSourceId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventTriggerSources
  COLUMN_NAME: EventTriggerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.EventTriggerSources
  COLUMN_NAME: EventSourceGuid
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventTriggerSources
  COLUMN_NAME: EventType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventTriggerSources
  COLUMN_NAME: EventValue
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventTriggerSources
  COLUMN_NAME: IsPrimaryEvent
  DATA_TYPE: bit
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.EventTriggerSources
  COLUMN_NAME: CameraId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.EventTriggerSources
  COLUMN_NAME: IOTriggerId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.EventTriggerSources
  COLUMN_NAME: VolumeId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.EventTriggerSources
  COLUMN_NAME: UserId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.IoInputs
  COLUMN_NAME: IoInputId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rs.IoInputs
  COLUMN_NAME: IOTriggerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.IoInputs
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.IoInputs
  COLUMN_NAME: Name
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 80
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.IoInputs
  COLUMN_NAME: PinNo
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.IoInputs
  COLUMN_NAME: TriggerStateType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.IoInputs
  COLUMN_NAME: EventType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.IoOutputCameras
  COLUMN_NAME: IoOutputCameraId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rs.IoOutputCameras
  COLUMN_NAME: IoTriggerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.IoOutputCameras
  COLUMN_NAME: IoOutputId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.IoOutputCameras
  COLUMN_NAME: CameraId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.IoOutputs
  COLUMN_NAME: IoOutputId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rs.IoOutputs
  COLUMN_NAME: IOTriggerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.IoOutputs
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.IoOutputs
  COLUMN_NAME: Name
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 80
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.IoOutputs
  COLUMN_NAME: PinNo
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.IoOutputs
  COLUMN_NAME: TriggerStateType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.IoOutputs
  COLUMN_NAME: ResetTimeMs
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.IoOutputs
  COLUMN_NAME: DeviceType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.IoOutputs
  COLUMN_NAME: WillAutoReset
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.IOTriggers
  COLUMN_NAME: IOTriggerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rs.IOTriggers
  COLUMN_NAME: RecordingServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.IOTriggers
  COLUMN_NAME: IODeviceId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.IOTriggers
  COLUMN_NAME: IOTriggerGuid
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.IOTriggers
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.IOTriggers
  COLUMN_NAME: Name
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 64
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.IOTriggers
  COLUMN_NAME: Username
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 64
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.IOTriggers
  COLUMN_NAME: Password
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.IOTriggers
  COLUMN_NAME: DriverName
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.IOTriggers
  COLUMN_NAME: IpAddress
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.IOTriggers
  COLUMN_NAME: Note
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 11
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.Loggers
  COLUMN_NAME: LoggerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rs.Loggers
  COLUMN_NAME: LogSettingId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.Loggers
  COLUMN_NAME: ComponentNo
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.Loggers
  COLUMN_NAME: PriorityNo
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.Loggers
  COLUMN_NAME: DoFlushOnWrite
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.Loggers
  COLUMN_NAME: DoRolloverOnSaveCfg
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.Loggers
  COLUMN_NAME: KeepForDays
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.LogSettings
  COLUMN_NAME: LogSettingId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rs.LogSettings
  COLUMN_NAME: LogFilePath
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 512
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.MediaEncryptions
  COLUMN_NAME: ServerGuid
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rs.MediaEncryptions
  COLUMN_NAME: Enabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.MediaEncryptions
  COLUMN_NAME: CipherMode
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.MediaEncryptions
  COLUMN_NAME: KeySize
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.MediaEncryptions
  COLUMN_NAME: PasswordBase64
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 1024
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.MediaEncryptions
  COLUMN_NAME: Iterations
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.MediaEncryptions
  COLUMN_NAME: HashAlgorithm
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.MediaEncryptions
  COLUMN_NAME: KekBase64
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 512
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.MediaEncryptions
  COLUMN_NAME: DekBase64
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 512
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.MediaEncryptions
  COLUMN_NAME: DekCreatedOnUtc
  DATA_TYPE: datetime
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.MediaEncryptions
  COLUMN_NAME: Note
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 11
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.MediaEncryptions
  COLUMN_NAME: WriteToken
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 12
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.NVRCameras
  COLUMN_NAME: NVRCameraId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rs.NVRCameras
  COLUMN_NAME: NVRId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.NVRCameras
  COLUMN_NAME: CameraId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.NVRCameras
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.NVRCameras
  COLUMN_NAME: ChannelNo
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.NVRCameras
  COLUMN_NAME: IsVmsVolume
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.NVRCameras
  COLUMN_NAME: IsNvrStorage
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.NVRs
  COLUMN_NAME: NVRId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rs.NVRs
  COLUMN_NAME: RecordingServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.NVRs
  COLUMN_NAME: VideoDeviceId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.NVRs
  COLUMN_NAME: NVRGuid
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.NVRs
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.NVRs
  COLUMN_NAME: Name
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 64
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.NVRs
  COLUMN_NAME: Manufacturer
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 80
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.NVRs
  COLUMN_NAME: Model
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.NVRs
  COLUMN_NAME: ChannelCount
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.NVRs
  COLUMN_NAME: TimeZone
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 64
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.NVRs
  COLUMN_NAME: HttpPort
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 11
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.NVRs
  COLUMN_NAME: RtspPort
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 12
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.NVRs
  COLUMN_NAME: ControlPort
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 13
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.NVRs
  COLUMN_NAME: HostAddress
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 14
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.NVRs
  COLUMN_NAME: Username
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 64
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 15
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.NVRs
  COLUMN_NAME: Password
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 16
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.NVRs
  COLUMN_NAME: TimeoutSecs
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 17
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.NVRs
  COLUMN_NAME: NumRetries
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 18
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.NVRs
  COLUMN_NAME: Note
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 19
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.PresetZones
  COLUMN_NAME: PresetZoneId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rs.PresetZones
  COLUMN_NAME: RecordingServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.PresetZones
  COLUMN_NAME: PriorityType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.PresetZones
  COLUMN_NAME: HoldTimeSeconds
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.PresetZones
  COLUMN_NAME: DwellTimeSeconds
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.PresetZones
  COLUMN_NAME: IsCyclingEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.RecordingServers
  COLUMN_NAME: RecordingServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rs.RecordingServers
  COLUMN_NAME: RecordingServerGuid
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.RecordingServers
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.RecordingServers
  COLUMN_NAME: Name
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 250
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.RecordingServers
  COLUMN_NAME: Description
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.RecordingServers
  COLUMN_NAME: TimeZone
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 40
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.RecordingServers
  COLUMN_NAME: Version
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 40
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.RecordingServers
  COLUMN_NAME: ProductId
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 100
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.RecordingServers
  COLUMN_NAME: UseTls
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.RecordingServers
  COLUMN_NAME: IpLicensesUsed
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.RecordingServers
  COLUMN_NAME: AnalogLicensesUsed
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 11
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.RecordingServers
  COLUMN_NAME: IsLegacy
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 12
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.RecordingServers
  COLUMN_NAME: Platform
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 64
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 13
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.RecordingServers
  COLUMN_NAME: SerialNumber
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 64
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 14
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.RecordingServers
  COLUMN_NAME: WasAutoProvisioned
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 15
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.RecordingServers
  COLUMN_NAME: LastSync
  DATA_TYPE: datetime
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 16
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.RecordingServers
  COLUMN_NAME: FeatureKeyVersion
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 17
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.RecordingServers
  COLUMN_NAME: Latitude
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 20
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 18
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.RecordingServers
  COLUMN_NAME: Longitude
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 20
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 19
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.RecordingServers
  COLUMN_NAME: PortId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 20
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.RecordingServers
  COLUMN_NAME: ConnectionId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 21
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.RecordingServers
  COLUMN_NAME: LicenseId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 22
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.RecordingServers
  COLUMN_NAME: LocalWebServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 23
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.RecordingServers
  COLUMN_NAME: LocalSecureWebServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 24
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.RecordingServers
  COLUMN_NAME: LogSettingId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 25
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.RecordingServers
  COLUMN_NAME: EventNotificationId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 26
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.RecordingServers
  COLUMN_NAME: UdpBroadcastId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 27
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.RecordingServers
  COLUMN_NAME: PrimaryRecordingServerFailoverConfigId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 28
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.RecordingServers
  COLUMN_NAME: StandbyRecordingServerFailoverConfigId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 29
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.RecordingServers
  COLUMN_NAME: Note
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 30
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.UdpBroadcasts
  COLUMN_NAME: UdpBroadcastId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rs.UdpBroadcasts
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.UdpBroadcasts
  COLUMN_NAME: Port
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.UdpBroadcasts
  COLUMN_NAME: RepeatCount
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.UdpBroadcasts
  COLUMN_NAME: WaitTimeoutMs
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VideoDecoders
  COLUMN_NAME: VideoDecoderId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VideoDecoders
  COLUMN_NAME: RecordingServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.VideoDecoders
  COLUMN_NAME: VideoCompressionType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VideoDecoders
  COLUMN_NAME: TranscoderEngineType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VideoDecoders
  COLUMN_NAME: VideoCompressionProfileType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VideoDecoders
  COLUMN_NAME: LatencyType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VideoDecoders
  COLUMN_NAME: VideoCompressionRateType
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VideoDecoders
  COLUMN_NAME: BitRate
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VideoDecoders
  COLUMN_NAME: GopLength
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VideoDecoders
  COLUMN_NAME: HardwareAccelerationType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VideoEncoders
  COLUMN_NAME: VideoEncoderId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VideoEncoders
  COLUMN_NAME: RecordingServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.VideoEncoders
  COLUMN_NAME: VideoCompressionType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VideoEncoders
  COLUMN_NAME: TranscoderEngineType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VideoEncoders
  COLUMN_NAME: VideoCompressionProfileType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VideoEncoders
  COLUMN_NAME: LatencyType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VideoEncoders
  COLUMN_NAME: VideoCompressionRateType
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VideoEncoders
  COLUMN_NAME: BitRate
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VideoEncoders
  COLUMN_NAME: GopLength
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VideoEncoders
  COLUMN_NAME: HardwareAccelerationType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VolumeExpirations
  COLUMN_NAME: VolumeExpirationId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VolumeExpirations
  COLUMN_NAME: VolumeId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.VolumeExpirations
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VolumeExpirations
  COLUMN_NAME: StartTime
  DATA_TYPE: time
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VolumeExpirations
  COLUMN_NAME: Frequency
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VolumeExpirations
  COLUMN_NAME: MaxVideoDays
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VolumeExpirations
  COLUMN_NAME: IsScheduleClips
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VolumeExpirations
  COLUMN_NAME: IsMotionClips
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VolumeExpirations
  COLUMN_NAME: IsAlarmClips
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VolumeExports
  COLUMN_NAME: VolumeExportId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VolumeExports
  COLUMN_NAME: VolumeId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.VolumeExports
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VolumeExports
  COLUMN_NAME: StartTime
  DATA_TYPE: time
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VolumeExports
  COLUMN_NAME: Frequency
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VolumeExports
  COLUMN_NAME: WillStoreAllExports
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VolumeExports
  COLUMN_NAME: WillStoreServerExportQueue
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VolumeRetentions
  COLUMN_NAME: VolumeRetentionId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VolumeRetentions
  COLUMN_NAME: VolumeId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.VolumeRetentions
  COLUMN_NAME: ArchiveVolumeId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.VolumeRetentions
  COLUMN_NAME: BackupVolumeId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.VolumeRetentions
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VolumeRetentions
  COLUMN_NAME: StartTime
  DATA_TYPE: time
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VolumeRetentions
  COLUMN_NAME: Frequency
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VolumeRetentions
  COLUMN_NAME: RetentionDays
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VolumeRetentions
  COLUMN_NAME: IsScheduleClips
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VolumeRetentions
  COLUMN_NAME: IsMotionClips
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.VolumeRetentions
  COLUMN_NAME: IsAlarmClips
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 11
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.Volumes
  COLUMN_NAME: VolumeId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rs.Volumes
  COLUMN_NAME: RecordingServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rs.Volumes
  COLUMN_NAME: VolumeGuid
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.Volumes
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.Volumes
  COLUMN_NAME: VolumeType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.Volumes
  COLUMN_NAME: Name
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 64
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.Volumes
  COLUMN_NAME: Path
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.Volumes
  COLUMN_NAME: UncPath
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 2000
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.Volumes
  COLUMN_NAME: FreeSpacePercentage
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.Volumes
  COLUMN_NAME: DesiredMinVideoDays
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rs.Volumes
  COLUMN_NAME: Note
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 11
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.AudioDevices
  COLUMN_NAME: AudioDeviceId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.AudioDevices
  COLUMN_NAME: DeviceGuid
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.AudioDevices
  COLUMN_NAME: Name
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.AudioDevices
  COLUMN_NAME: DriverType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.AudioDevices
  COLUMN_NAME: ConnectionType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.AudioDevices
  COLUMN_NAME: InternalNo
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.AudioDevices
  COLUMN_NAME: DeviceAttributes
  DATA_TYPE: bigint
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 19
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.AudioDevices
  COLUMN_NAME: StatusType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.AudioDevices
  COLUMN_NAME: WasAutoDetected
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.AudioDevices
  COLUMN_NAME: LastSync
  DATA_TYPE: datetime
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.AudioDevices
  COLUMN_NAME: Note
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 11
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.AudioDevices
  COLUMN_NAME: Manufacturer
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 80
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 12
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.AudioDevices
  COLUMN_NAME: Model
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 13
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.AudioDevices
  COLUMN_NAME: Quality
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 14
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.AudioDevices
  COLUMN_NAME: ChannelMode
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 15
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.AudioDevices
  COLUMN_NAME: IpV4Address
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 16
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.AudioDevices
  COLUMN_NAME: IpV6Address
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 17
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.AudioDevices
  COLUMN_NAME: ComPort
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 18
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.AudioDeviceServers
  COLUMN_NAME: AudioDeviceServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.AudioDeviceServers
  COLUMN_NAME: AudioDeviceId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rsd.AudioDeviceServers
  COLUMN_NAME: RecordingServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rsd.CameraEvents
  COLUMN_NAME: CameraEventId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.CameraEvents
  COLUMN_NAME: VideoDeviceId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rsd.CameraEvents
  COLUMN_NAME: CameraEventType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.CameraEvents
  COLUMN_NAME: Category
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.CameraEvents
  COLUMN_NAME: EventName
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.CameraEvents
  COLUMN_NAME: SubEventName
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.CameraEvents
  COLUMN_NAME: OnEventType
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.CameraEvents
  COLUMN_NAME: OffEventType
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.CameraEvents
  COLUMN_NAME: IsPulsed
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.DeviceTriggers
  COLUMN_NAME: DeviceTriggerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.DeviceTriggers
  COLUMN_NAME: VideoDeviceId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rsd.DeviceTriggers
  COLUMN_NAME: IsInput
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.DeviceTriggers
  COLUMN_NAME: Name
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 64
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.DeviceTriggers
  COLUMN_NAME: IdleState
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.DiskDevices
  COLUMN_NAME: DiskDeviceId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.DiskDevices
  COLUMN_NAME: DeviceGuid
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.DiskDevices
  COLUMN_NAME: Name
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.DiskDevices
  COLUMN_NAME: DriverType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.DiskDevices
  COLUMN_NAME: ConnectionType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.DiskDevices
  COLUMN_NAME: InternalNo
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.DiskDevices
  COLUMN_NAME: DeviceAttributes
  DATA_TYPE: bigint
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 19
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.DiskDevices
  COLUMN_NAME: StatusType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.DiskDevices
  COLUMN_NAME: WasAutoDetected
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.DiskDevices
  COLUMN_NAME: LastSync
  DATA_TYPE: datetime
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.DiskDevices
  COLUMN_NAME: Note
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 11
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.DiskDevices
  COLUMN_NAME: TotalSizeMB
  DATA_TYPE: bigint
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 19
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 12
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.DiskDevices
  COLUMN_NAME: FreeSpaceMB
  DATA_TYPE: bigint
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 19
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 13
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.DiskDevices
  COLUMN_NAME: IsIndexingEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 14
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.DiskDeviceServers
  COLUMN_NAME: DiskDeviceServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.DiskDeviceServers
  COLUMN_NAME: DiskDeviceId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rsd.DiskDeviceServers
  COLUMN_NAME: RecordingServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rsd.IODevices
  COLUMN_NAME: IODeviceId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.IODevices
  COLUMN_NAME: DeviceGuid
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.IODevices
  COLUMN_NAME: Name
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.IODevices
  COLUMN_NAME: DriverType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.IODevices
  COLUMN_NAME: ConnectionType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.IODevices
  COLUMN_NAME: InternalNo
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.IODevices
  COLUMN_NAME: DeviceAttributes
  DATA_TYPE: bigint
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 19
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.IODevices
  COLUMN_NAME: StatusType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.IODevices
  COLUMN_NAME: WasAutoDetected
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.IODevices
  COLUMN_NAME: LastSync
  DATA_TYPE: datetime
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.IODevices
  COLUMN_NAME: Note
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 11
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.IODevices
  COLUMN_NAME: ComPort
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 12
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.IODevices
  COLUMN_NAME: InputCount
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 13
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.IODevices
  COLUMN_NAME: OutputCount
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 14
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.IODevices
  COLUMN_NAME: Manufacturer
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 80
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 15
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.IODevices
  COLUMN_NAME: Model
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 16
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.IODevices
  COLUMN_NAME: IpV4Address
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 17
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.IODevices
  COLUMN_NAME: IpV6Address
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 18
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.IODeviceServers
  COLUMN_NAME: IODeviceServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.IODeviceServers
  COLUMN_NAME: IODeviceId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rsd.IODeviceServers
  COLUMN_NAME: RecordingServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rsd.MediaProfiles
  COLUMN_NAME: MediaProfileId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.MediaProfiles
  COLUMN_NAME: VideoDeviceId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rsd.MediaProfiles
  COLUMN_NAME: ChannelNo
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.MediaProfiles
  COLUMN_NAME: Name
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.MediaProfiles
  COLUMN_NAME: Token
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 64
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.MediaProfiles
  COLUMN_NAME: SnapshotUrl
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 500
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.MediaProfiles
  COLUMN_NAME: StreamUrl
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 500
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.MediaProfiles
  COLUMN_NAME: EncodingProfile
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.MediaProfiles
  COLUMN_NAME: VideoEncoderType
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.MediaProfiles
  COLUMN_NAME: MaxWidth
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.MediaProfiles
  COLUMN_NAME: MaxHeight
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 11
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.MediaProfiles
  COLUMN_NAME: Resolutions
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: -1
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 12
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.MediaProfiles
  COLUMN_NAME: MinGovLength
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 13
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.MediaProfiles
  COLUMN_NAME: MaxGovLength
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 14
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.MediaProfiles
  COLUMN_NAME: GovLength
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 15
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.MediaProfiles
  COLUMN_NAME: MinFps
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 16
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.MediaProfiles
  COLUMN_NAME: MaxFps
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 17
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.MediaProfiles
  COLUMN_NAME: Fps
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 18
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.MediaProfiles
  COLUMN_NAME: MinQuality
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 19
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.MediaProfiles
  COLUMN_NAME: MaxQuality
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 20
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.MediaProfiles
  COLUMN_NAME: Quality
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 21
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.MediaProfiles
  COLUMN_NAME: MinBitrate
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 22
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.MediaProfiles
  COLUMN_NAME: MaxBitrate
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 23
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.MediaProfiles
  COLUMN_NAME: Bitrate
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 24
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.MediaProfiles
  COLUMN_NAME: IsAudioAvailable
  DATA_TYPE: bit
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 25
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.MediaProfiles
  COLUMN_NAME: AudioEncodingType
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 26
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.MediaProfiles
  COLUMN_NAME: AudioBitrate
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 27
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.MediaProfiles
  COLUMN_NAME: AudioSampleRate
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 28
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.SerialDevices
  COLUMN_NAME: SerialDeviceId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.SerialDevices
  COLUMN_NAME: DeviceGuid
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.SerialDevices
  COLUMN_NAME: Name
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.SerialDevices
  COLUMN_NAME: DriverType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.SerialDevices
  COLUMN_NAME: ConnectionType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.SerialDevices
  COLUMN_NAME: InternalNo
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.SerialDevices
  COLUMN_NAME: DeviceAttributes
  DATA_TYPE: bigint
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 19
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.SerialDevices
  COLUMN_NAME: StatusType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.SerialDevices
  COLUMN_NAME: WasAutoDetected
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.SerialDevices
  COLUMN_NAME: LastSync
  DATA_TYPE: datetime
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.SerialDevices
  COLUMN_NAME: Note
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 11
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.SerialDevices
  COLUMN_NAME: PortNo
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 12
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.SerialDevices
  COLUMN_NAME: BaudRate
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 13
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.SerialDevices
  COLUMN_NAME: DataBits
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 14
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.SerialDevices
  COLUMN_NAME: ParityType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 15
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.SerialDevices
  COLUMN_NAME: StopBits
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 16
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.SerialDevices
  COLUMN_NAME: FlowControl
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 17
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.SerialDeviceServers
  COLUMN_NAME: SerialDeviceServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.SerialDeviceServers
  COLUMN_NAME: SerialDeviceId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rsd.SerialDeviceServers
  COLUMN_NAME: RecordingServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: VideoDeviceId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: ProviderServerId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: DeviceGuid
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: Name
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: DriverType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: ConnectionType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: InternalNo
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: DeviceAttributes
  DATA_TYPE: bigint
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 19
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: StatusType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: WasAutoDetected
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: LastSync
  DATA_TYPE: datetime
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 11
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: Note
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 12
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: MacAddress
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 13
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: SerialNumber
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 14
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: Manufacturer
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 80
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 15
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: Model
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 16
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: IsPtz
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 17
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: ChannelCount
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 18
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: PresetCount
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 19
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: IpV4Address
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 20
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: IpV6Address
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 21
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: HttpPort
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 22
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: HttpsPort
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 23
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: RtspPort
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 24
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: Firmware
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 60
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 25
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: VideoStandardType
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 26
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: SourceFilePath
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 500
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 27
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: OnvifDeviceUrl
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 64
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 28
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: OnvifPtzUrl
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 64
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 29
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: OnvifEventUrl
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 64
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 30
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: StretchSdkVersion
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 32
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 31
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: StretchDriverVersion
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 32
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 32
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: StretchFirmwareVersion
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 32
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 33
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: StretchBootLoaderVersion
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 32
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 34
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: StretchBspVersion
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 32
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 35
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: StretchPciSlotNumber
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 36
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: StretchCardIsPresent
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 37
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: OnBoardEventsLastSync
  DATA_TYPE: datetime
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 38
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: SupportsAudio
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: ((0))
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 39
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDevices
  COLUMN_NAME: VideoCompressionsArr
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 64
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 40
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDeviceServers
  COLUMN_NAME: VideoDeviceServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rsd.VideoDeviceServers
  COLUMN_NAME: VideoDeviceId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rsd.VideoDeviceServers
  COLUMN_NAME: RecordingServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rsf.PrimariesStandbyServers
  COLUMN_NAME: PrimaryStandbyServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rsf.PrimariesStandbyServers
  COLUMN_NAME: PrimaryRecordingServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rsf.PrimariesStandbyServers
  COLUMN_NAME: StandbyRecordingServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rsf.PrimaryRecordingServerFailoverConfigs
  COLUMN_NAME: PrimaryRecordingServerFailoverConfigId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rsf.PrimaryRecordingServerFailoverConfigs
  COLUMN_NAME: FailoverAfterMS
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsf.PrimaryRecordingServerFailoverConfigs
  COLUMN_NAME: RestoreAfterMS
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsf.StandbyRecordingServerFailoverConfigs
  COLUMN_NAME: StandbyRecordingServerFailoverConfigId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rsf.StandbyRecordingServerFailoverConfigs
  COLUMN_NAME: PrimaryRecordingServerId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rsf.StandbyRecordingServerFailoverConfigs
  COLUMN_NAME: SharePath
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsi.AgentVis
  COLUMN_NAME: AgentViId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rsi.AgentVis
  COLUMN_NAME: RecordingServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rsi.AgentVis
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsi.AgentVis
  COLUMN_NAME: EngineAddress
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsi.AgentVis
  COLUMN_NAME: ProxyLocalPort
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsi.AxisOneClicks
  COLUMN_NAME: AxisOneClickId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rsi.AxisOneClicks
  COLUMN_NAME: RecordingServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rsi.AxisOneClicks
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsi.AxisOneClicks
  COLUMN_NAME: DispatchServerAddress
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsi.AxisOneClicks
  COLUMN_NAME: UserName
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 64
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsi.AxisOneClicks
  COLUMN_NAME: Password
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsi.AxisOneClicks
  COLUMN_NAME: ProxyExternalAddress
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsi.AxisOneClicks
  COLUMN_NAME: ProxyExternalPort
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsi.AxisOneClicks
  COLUMN_NAME: ProxyInternalAddress
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsi.AxisOneClicks
  COLUMN_NAME: ProxyInternalPort
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsi.AxisOneClicks
  COLUMN_NAME: OcccVersion
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 11
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsi.AxisOneClicks
  COLUMN_NAME: CertificatePath
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 12
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsi.Bolds
  COLUMN_NAME: BoldId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rsi.Bolds
  COLUMN_NAME: RecordingServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rsi.Bolds
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsi.Bolds
  COLUMN_NAME: ManitouServerAddress
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 64
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsi.Bolds
  COLUMN_NAME: ManitouPort
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsi.Bolds
  COLUMN_NAME: HeartbeatInterval
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsi.BriefCams
  COLUMN_NAME: BriefCamId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rsi.BriefCams
  COLUMN_NAME: RecordingServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rsi.BriefCams
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsi.BriefCams
  COLUMN_NAME: Address
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsi.BriefCams
  COLUMN_NAME: Port
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsi.BriefCams
  COLUMN_NAME: UserName
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 64
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsi.BriefCams
  COLUMN_NAME: Password
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsi.BriefCams
  COLUMN_NAME: AdminPath
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsi.BriefCams
  COLUMN_NAME: UserPath
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsi.S2s
  COLUMN_NAME: S2Id
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rsi.S2s
  COLUMN_NAME: RecordingServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rsi.S2s
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsi.S2s
  COLUMN_NAME: S2ControllerAddress
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 64
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsi.SureViews
  COLUMN_NAME: SureViewId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rsi.SureViews
  COLUMN_NAME: RecordingServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rsi.SureViews
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsi.SureViews
  COLUMN_NAME: ImmixDeviceNumber
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsi.SureViews
  COLUMN_NAME: ImmixSmtpServer
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsi.SureViews
  COLUMN_NAME: ImmixSmtpPort
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsi.SureViews
  COLUMN_NAME: ImmixNumberOfSnapshots
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsi.SureViews
  COLUMN_NAME: ImmixDurationSeconds
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsp.StorageDrives
  COLUMN_NAME: StoragePoolGuid
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rsp.StorageDrives
  COLUMN_NAME: RecordingServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rsp.StorageDrives
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsp.StorageDrives
  COLUMN_NAME: DrivePath
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rsp.StorageDrives
  COLUMN_NAME: VideoSpacePercent
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsp.StorageDrives
  COLUMN_NAME: IsOverflow
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsp.StoragePools
  COLUMN_NAME: StoragePoolGuid
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: rsp.StoragePools
  COLUMN_NAME: RecordingServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: rsp.StoragePools
  COLUMN_NAME: PoolType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsp.StoragePools
  COLUMN_NAME: WarningLevelPercent
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: rsp.StoragePoolSettings
  COLUMN_NAME: RecordingServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: YES

  FullTableName: rsp.StoragePoolSettings
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: shl.CameraSchedules
  COLUMN_NAME: CameraScheduleId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: shl.CameraSchedules
  COLUMN_NAME: ScheduleId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: shl.CameraSchedules
  COLUMN_NAME: CameraId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: shl.EventTriggerSchedules
  COLUMN_NAME: EventTriggerScheduleId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: shl.EventTriggerSchedules
  COLUMN_NAME: ScheduleId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: shl.EventTriggerSchedules
  COLUMN_NAME: EventTriggerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: shl.ScheduledTimeSegments
  COLUMN_NAME: ScheduledTimeSegmentId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: shl.ScheduledTimeSegments
  COLUMN_NAME: ScheduleId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: shl.ScheduledTimeSegments
  COLUMN_NAME: ScheduleOperationId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: shl.ScheduledTimeSegments
  COLUMN_NAME: OnDate
  DATA_TYPE: date
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: shl.ScheduledTimeSegments
  COLUMN_NAME: DayOfWeekType
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: shl.ScheduledTimeSegments
  COLUMN_NAME: StartTime
  DATA_TYPE: time
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: shl.ScheduledTimeSegments
  COLUMN_NAME: EndTime
  DATA_TYPE: time
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: shl.ScheduleOperations
  COLUMN_NAME: ScheduleOperationId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: shl.ScheduleOperations
  COLUMN_NAME: ScheduleId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: shl.ScheduleOperations
  COLUMN_NAME: OperationType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: shl.Schedules
  COLUMN_NAME: ScheduleId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: shl.Schedules
  COLUMN_NAME: RecordingServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: shl.Schedules
  COLUMN_NAME: ScheduleGuid
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: shl.Schedules
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: shl.Schedules
  COLUMN_NAME: ResourceType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: shl.Schedules
  COLUMN_NAME: Name
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 80
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: shl.Schedules
  COLUMN_NAME: IsDefault
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: shl.Schedules
  COLUMN_NAME: Note
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: tmp.TmpGuids
  COLUMN_NAME: Guid
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: tmp.TmpGuids
  COLUMN_NAME: Value
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: tmp.TmpInts
  COLUMN_NAME: Id
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: tmp.TmpInts
  COLUMN_NAME: IntValue
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: tmp.TmpStrings
  COLUMN_NAME: Guid
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: tmp.TmpStrings
  COLUMN_NAME: Value
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 1024
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Configurations
  COLUMN_NAME: ConfigurationId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Configurations
  COLUMN_NAME: IsAppAutoLoginChecked
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Configurations
  COLUMN_NAME: IsAppCustomFavoritesChecked
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Configurations
  COLUMN_NAME: IsAppServersCamerasChecked
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Configurations
  COLUMN_NAME: IsAppServerIpAddressChecked
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Configurations
  COLUMN_NAME: IsAppAutoFullscreenChecked
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Configurations
  COLUMN_NAME: IsPtzChecked
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Configurations
  COLUMN_NAME: IsLvAutoSequenceChecked
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Configurations
  COLUMN_NAME: IsLvVideoPanelChecked
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Configurations
  COLUMN_NAME: IsLvEventPanelChecked
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Configurations
  COLUMN_NAME: IsLvMapPanelChecked
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 11
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Configurations
  COLUMN_NAME: IsLvWallPanelChecked
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 12
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Configurations
  COLUMN_NAME: IsMaxTileCountEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 13
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Configurations
  COLUMN_NAME: MaxTileCount
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 14
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Configurations
  COLUMN_NAME: IsAvMapPanelChecked
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 15
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Configurations
  COLUMN_NAME: IsAvCameraPanelChecked
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 16
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Configurations
  COLUMN_NAME: PlayersType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 17
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Configurations
  COLUMN_NAME: IsDbUserConnectionsChecked
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 18
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Configurations
  COLUMN_NAME: IsDbSearchVideoChecked
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 19
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Configurations
  COLUMN_NAME: IsDbSearchEventsChecked
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 20
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.PasswordHistory
  COLUMN_NAME: PasswordHistoryId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: usr.PasswordHistory
  COLUMN_NAME: UserId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: usr.PasswordHistory
  COLUMN_NAME: SetOn
  DATA_TYPE: datetime
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.PasswordHistory
  COLUMN_NAME: Password
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.PasswordPolicies
  COLUMN_NAME: PasswordPolicyId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: usr.PasswordPolicies
  COLUMN_NAME: PolicyJson
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 4000
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Roles
  COLUMN_NAME: RoleId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Roles
  COLUMN_NAME: Identifier
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 20
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Roles
  COLUMN_NAME: Name
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 60
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserCameras
  COLUMN_NAME: UserCameraId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserCameras
  COLUMN_NAME: ConfigurationId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: usr.UserCameras
  COLUMN_NAME: CameraId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: usr.UserCameras
  COLUMN_NAME: IsLiveChecked
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserCameras
  COLUMN_NAME: IsPlaybackChecked
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserCameras
  COLUMN_NAME: IsAudioChecked
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserCameras
  COLUMN_NAME: IsExportChecked
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserCameras
  COLUMN_NAME: IsSnapshotChecked
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserCameras
  COLUMN_NAME: IsLightChecked
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserCameras
  COLUMN_NAME: IsPtzChecked
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserCameras
  COLUMN_NAME: PlaybackTimeout
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 11
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserCameras
  COLUMN_NAME: IsFavorite
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 12
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserEvents
  COLUMN_NAME: UserEventId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserEvents
  COLUMN_NAME: ConfigurationId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: usr.UserEvents
  COLUMN_NAME: EventType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserEvents
  COLUMN_NAME: IsChecked
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserEventTriggers
  COLUMN_NAME: UserEventTriggerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserEventTriggers
  COLUMN_NAME: ConfigurationId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: usr.UserEventTriggers
  COLUMN_NAME: EventTriggerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: usr.UserEventTriggers
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserEventTriggers
  COLUMN_NAME: WillExecute
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserEventTriggers
  COLUMN_NAME: IsFavorite
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserInputs
  COLUMN_NAME: UserInputId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserInputs
  COLUMN_NAME: ConfigurationId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: usr.UserInputs
  COLUMN_NAME: IOInputId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: usr.UserInputs
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserInputs
  COLUMN_NAME: IsFavorite
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserModules
  COLUMN_NAME: UserModuleId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserModules
  COLUMN_NAME: ConfigurationId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: usr.UserModules
  COLUMN_NAME: ModuleType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserModules
  COLUMN_NAME: HasAccess
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserModules
  COLUMN_NAME: IsSetAtStartup
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserOutputs
  COLUMN_NAME: UserOutputId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserOutputs
  COLUMN_NAME: ConfigurationId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: usr.UserOutputs
  COLUMN_NAME: IOOutputId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: usr.UserOutputs
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserOutputs
  COLUMN_NAME: WillExecute
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserOutputs
  COLUMN_NAME: IsFavorite
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserPasswordPolicies
  COLUMN_NAME: UserId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: YES

  FullTableName: usr.UserPasswordPolicies
  COLUMN_NAME: PolicyJson
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 4000
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserPresets
  COLUMN_NAME: UserPresetId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserPresets
  COLUMN_NAME: ConfigurationId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: usr.UserPresets
  COLUMN_NAME: CameraId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: usr.UserPresets
  COLUMN_NAME: PresetIndex
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserPresets
  COLUMN_NAME: IsSetChecked
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserPresets
  COLUMN_NAME: IsShowChecked
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserRoles
  COLUMN_NAME: UserRoleId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserRoles
  COLUMN_NAME: ConfigurationId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: usr.UserRoles
  COLUMN_NAME: RoleId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: usr.Users
  COLUMN_NAME: UserId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Users
  COLUMN_NAME: ConfigurationId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: usr.Users
  COLUMN_NAME: UserGuid
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Users
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Users
  COLUMN_NAME: IsAdmin
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Users
  COLUMN_NAME: RemoteAccess
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Users
  COLUMN_NAME: ActiveDirectoryId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: usr.Users
  COLUMN_NAME: SubDomain
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Users
  COLUMN_NAME: Username
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 500
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Users
  COLUMN_NAME: Password
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Users
  COLUMN_NAME: IsDefaultPassword
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 11
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Users
  COLUMN_NAME: PtzPriority
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 12
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Users
  COLUMN_NAME: IsManaged
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 13
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Users
  COLUMN_NAME: ExpiresOn
  DATA_TYPE: datetime
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 14
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Users
  COLUMN_NAME: Note
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 15
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Users
  COLUMN_NAME: LastName
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 250
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 16
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Users
  COLUMN_NAME: Email
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 250
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 17
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Users
  COLUMN_NAME: ResetOn
  DATA_TYPE: datetime
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 18
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Users
  COLUMN_NAME: FirstName
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 19
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Users
  COLUMN_NAME: Phone
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 20
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Users
  COLUMN_NAME: Address
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 21
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Users
  COLUMN_NAME: Cloud
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: ((0))
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 22
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.Users
  COLUMN_NAME: MsOwnerGuid
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 23
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserServers
  COLUMN_NAME: UserServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserServers
  COLUMN_NAME: ConfigurationId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: usr.UserServers
  COLUMN_NAME: RecordingServerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: usr.UserServers
  COLUMN_NAME: IsAdminChecked
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserServers
  COLUMN_NAME: IsApiChecked
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserServers
  COLUMN_NAME: IsEventsChecked
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserServers
  COLUMN_NAME: PtzPriorityType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserServers
  COLUMN_NAME: IsFavorite
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserViews
  COLUMN_NAME: UserViewId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserViews
  COLUMN_NAME: ConfigurationId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: usr.UserViews
  COLUMN_NAME: ViewId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: usr.UserViews
  COLUMN_NAME: IsFavorite
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserViews
  COLUMN_NAME: Navigation
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: ((0))
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserViews
  COLUMN_NAME: ShowUrl
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: ((0))
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserViews
  COLUMN_NAME: LiveView
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: ((0))
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: usr.UserViews
  COLUMN_NAME: Playback
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: ((0))
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.CameraViews
  COLUMN_NAME: CameraViewId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: vw.CameraViews
  COLUMN_NAME: ViewId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: vw.CameraViews
  COLUMN_NAME: CameraId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: vw.CameraViews
  COLUMN_NAME: FitOption
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.CameraViewTriggers
  COLUMN_NAME: CameraViewTriggerId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: vw.CameraViewTriggers
  COLUMN_NAME: ViewId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: vw.CameraViewTriggers
  COLUMN_NAME: CameraViewId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: vw.CameraViewTriggers
  COLUMN_NAME: IoOutputId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: vw.FixedTiles
  COLUMN_NAME: FixedTileId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: vw.FixedTiles
  COLUMN_NAME: ViewId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: vw.FixedTiles
  COLUMN_NAME: TileId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: vw.FixedTiles
  COLUMN_NAME: PositionX
  DATA_TYPE: float
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 53
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.FixedTiles
  COLUMN_NAME: PositionY
  DATA_TYPE: float
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 53
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.FixedTiles
  COLUMN_NAME: Width
  DATA_TYPE: float
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 53
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.FixedTiles
  COLUMN_NAME: Height
  DATA_TYPE: float
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 53
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.MapImages
  COLUMN_NAME: MapImageId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: vw.MapImages
  COLUMN_NAME: ImageBytes
  DATA_TYPE: varbinary
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: -1
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.MapImages
  COLUMN_NAME: CompressionType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: ((0))
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.MapImages
  COLUMN_NAME: FileSize
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: ((0))
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.MapImages
  COLUMN_NAME: Md5
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.MapImages
  COLUMN_NAME: Height
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: ((0))
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.MapImages
  COLUMN_NAME: Width
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: ((0))
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.Maps
  COLUMN_NAME: MapId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: vw.Maps
  COLUMN_NAME: MapImageId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: vw.Maps
  COLUMN_NAME: MapGuid
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.Maps
  COLUMN_NAME: MapType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.Maps
  COLUMN_NAME: Name
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.Maps
  COLUMN_NAME: Address
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.Maps
  COLUMN_NAME: Latitude
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 20
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.Maps
  COLUMN_NAME: Longitude
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 20
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.Maps
  COLUMN_NAME: CompressionType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.Maps
  COLUMN_NAME: FileSize
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.Maps
  COLUMN_NAME: Md5
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 128
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 11
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.Maps
  COLUMN_NAME: Height
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 12
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.Maps
  COLUMN_NAME: Width
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 13
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.MapViewCameras
  COLUMN_NAME: MapViewCameraId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: vw.MapViewCameras
  COLUMN_NAME: ViewId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: vw.MapViewCameras
  COLUMN_NAME: MapViewId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: vw.MapViewCameras
  COLUMN_NAME: CameraId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: vw.MapViewCameras
  COLUMN_NAME: PositionX
  DATA_TYPE: float
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 53
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.MapViewCameras
  COLUMN_NAME: PositionY
  DATA_TYPE: float
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 53
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.MapViewLinks
  COLUMN_NAME: MapViewLinkId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: vw.MapViewLinks
  COLUMN_NAME: ViewId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: vw.MapViewLinks
  COLUMN_NAME: MapViewId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: vw.MapViewLinks
  COLUMN_NAME: LinkedViewId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: vw.MapViewLinks
  COLUMN_NAME: PositionX
  DATA_TYPE: float
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 53
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.MapViewLinks
  COLUMN_NAME: PositionY
  DATA_TYPE: float
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 53
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.MapViews
  COLUMN_NAME: MapViewId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: vw.MapViews
  COLUMN_NAME: ViewId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: vw.MapViews
  COLUMN_NAME: MapId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: vw.Pins
  COLUMN_NAME: PinId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: vw.Pins
  COLUMN_NAME: ViewId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: vw.Pins
  COLUMN_NAME: MapViewId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: vw.Pins
  COLUMN_NAME: Latitude
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 20
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.Pins
  COLUMN_NAME: Longitude
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 20
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.Pins
  COLUMN_NAME: PositionX
  DATA_TYPE: float
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 53
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.Pins
  COLUMN_NAME: PositionY
  DATA_TYPE: float
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 53
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.ResponsiveTiles
  COLUMN_NAME: ResponsiveTileId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: vw.ResponsiveTiles
  COLUMN_NAME: ViewId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: vw.ResponsiveTiles
  COLUMN_NAME: TileId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: vw.ResponsiveTiles
  COLUMN_NAME: ParentTileId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: vw.ResponsiveTiles
  COLUMN_NAME: LayoutStyle
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.ResponsiveTiles
  COLUMN_NAME: Ordinal
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.ResponsiveTiles
  COLUMN_NAME: AbsoluteWidth
  DATA_TYPE: float
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 53
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.ResponsiveTiles
  COLUMN_NAME: AbsoluteHeight
  DATA_TYPE: float
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 53
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.ResponsiveTiles
  COLUMN_NAME: PositionX
  DATA_TYPE: float
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 53
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.ResponsiveTiles
  COLUMN_NAME: PositionY
  DATA_TYPE: float
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 53
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.ResponsiveTiles
  COLUMN_NAME: Row
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 11
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.ResponsiveTiles
  COLUMN_NAME: Column
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 12
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.ResponsiveTiles
  COLUMN_NAME: RowSpan
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 13
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.ResponsiveTiles
  COLUMN_NAME: ColumnSpan
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 14
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.Tiles
  COLUMN_NAME: TileId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: vw.Tiles
  COLUMN_NAME: ViewId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.Tiles
  COLUMN_NAME: CameraViewId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: vw.Tiles
  COLUMN_NAME: MapViewId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: vw.Tiles
  COLUMN_NAME: WebViewId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: vw.Tiles
  COLUMN_NAME: WallViewId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: vw.Views
  COLUMN_NAME: ViewId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: vw.Views
  COLUMN_NAME: RelatedViewId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: vw.Views
  COLUMN_NAME: ViewGuid
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.Views
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.Views
  COLUMN_NAME: Name
  DATA_TYPE: nvarchar
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 500
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.Views
  COLUMN_NAME: IsTemplate
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.Views
  COLUMN_NAME: DeclaredType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.Views
  COLUMN_NAME: LayoutStyle
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.Views
  COLUMN_NAME: Width
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.Views
  COLUMN_NAME: Height
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.Views
  COLUMN_NAME: CreatedByUserId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 11
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: vw.Views
  COLUMN_NAME: AspectRatio
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 12
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.Views
  COLUMN_NAME: IsInTour
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 13
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.Views
  COLUMN_NAME: DwellTimeSeconds
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 14
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.Views
  COLUMN_NAME: Fps
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 15
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.Views
  COLUMN_NAME: Quality
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 16
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.Views
  COLUMN_NAME: ShowCones
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 17
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.Views
  COLUMN_NAME: ShowLabel
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 18
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.Views
  COLUMN_NAME: ScaleToFit
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 19
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.Views
  COLUMN_NAME: ZoomLevel
  DATA_TYPE: float
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 53
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 20
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.Views
  COLUMN_NAME: PreviewIcon
  DATA_TYPE: varbinary
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: -1
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 21
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.Views
  COLUMN_NAME: Note
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 22
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.WallViews
  COLUMN_NAME: WallViewId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: vw.WallViews
  COLUMN_NAME: ViewId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: vw.WallViews
  COLUMN_NAME: WvAgentDisplayId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: vw.WallViews
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.WallViews
  COLUMN_NAME: AspectRatio
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.WallViews
  COLUMN_NAME: RowCount
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.WallViews
  COLUMN_NAME: ColumnCount
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.WebViews
  COLUMN_NAME: WebViewId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: vw.WebViews
  COLUMN_NAME: ViewId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: vw.WebViews
  COLUMN_NAME: WebUrl
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 2048
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vw.WebViews
  COLUMN_NAME: IsAudioEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: ((1))
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vwt.TemplateTiles
  COLUMN_NAME: TemplateTileId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: vwt.TemplateTiles
  COLUMN_NAME: ViewTemplateId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: vwt.TemplateTiles
  COLUMN_NAME: ParentTemplateTileId
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: vwt.TemplateTiles
  COLUMN_NAME: TileType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vwt.TemplateTiles
  COLUMN_NAME: PointX
  DATA_TYPE: float
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 53
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vwt.TemplateTiles
  COLUMN_NAME: PointY
  DATA_TYPE: float
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 53
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vwt.TemplateTiles
  COLUMN_NAME: Height
  DATA_TYPE: float
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 53
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vwt.TemplateTiles
  COLUMN_NAME: Width
  DATA_TYPE: float
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 53
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 8
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vwt.TemplateTiles
  COLUMN_NAME: Row
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 9
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vwt.TemplateTiles
  COLUMN_NAME: Column
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 10
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vwt.TemplateTiles
  COLUMN_NAME: RowSpan
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 11
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vwt.TemplateTiles
  COLUMN_NAME: ColumnSpan
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 12
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vwt.ViewTemplates
  COLUMN_NAME: ViewTemplateId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: vwt.ViewTemplates
  COLUMN_NAME: ViewTemplateGuid
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vwt.ViewTemplates
  COLUMN_NAME: Name
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 64
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vwt.ViewTemplates
  COLUMN_NAME: ViewType
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vwt.ViewTemplates
  COLUMN_NAME: LayoutStyle
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: vwt.ViewTemplates
  COLUMN_NAME: ImageBytesBase64
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: -1
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: wall.WallViewAgents
  COLUMN_NAME: WallViewAgentId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: wall.WallViewAgents
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: wall.WallViewAgents
  COLUMN_NAME: AgentGuid
  DATA_TYPE: uniqueidentifier
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: wall.WallViewAgents
  COLUMN_NAME: Name
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 64
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: wall.WallViewAgents
  COLUMN_NAME: HostIp
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 64
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: wall.WallViewAgents
  COLUMN_NAME: PortNo
  DATA_TYPE: int
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: wall.WallViewAgents
  COLUMN_NAME: Note
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 256
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: wall.WvAgentDisplays
  COLUMN_NAME: WvAgentDisplayId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 1
  IS_PRIMARY_KEY: YES
  IS_FOREIGN_KEY: NO

  FullTableName: wall.WvAgentDisplays
  COLUMN_NAME: WallViewAgentId
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 2
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: YES

  FullTableName: wall.WvAgentDisplays
  COLUMN_NAME: IsEnabled
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 3
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: wall.WvAgentDisplays
  COLUMN_NAME: IsPrimary
  DATA_TYPE: bit
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 4
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: wall.WvAgentDisplays
  COLUMN_NAME: DisplayNo
  DATA_TYPE: int
  IS_NULLABLE: NO
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: None
  NUMERIC_PRECISION: 10
  NUMERIC_SCALE: 0
  ORDINAL_POSITION: 5
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: wall.WvAgentDisplays
  COLUMN_NAME: DeviceName
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 100
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 6
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO

  FullTableName: wall.WvAgentDisplays
  COLUMN_NAME: DisplayName
  DATA_TYPE: nvarchar
  IS_NULLABLE: YES
  COLUMN_DEFAULT: None
  CHARACTER_MAXIMUM_LENGTH: 100
  NUMERIC_PRECISION: None
  NUMERIC_SCALE: None
  ORDINAL_POSITION: 7
  IS_PRIMARY_KEY: NO
  IS_FOREIGN_KEY: NO


ENHANCED FOREIGN KEY RELATIONSHIPS:
----------------------------------
  FK_ConstraintName: FK_Cameras_AdvancedSettingId
  FK_Table: cam.Cameras
  FK_Column: AdvancedSettingId
  Referenced_Table: cam.AdvancedSettings
  Referenced_Column: AdvancedSettingId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_Cameras_Camera360LensId
  FK_Table: cam.Cameras
  FK_Column: Camera360LensId
  Referenced_Table: cam.Camera360Lens
  Referenced_Column: Camera360LensId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_Camera_ConeId
  FK_Table: cam.Cameras
  FK_Column: ConeId
  Referenced_Table: cam.Cones
  Referenced_Column: ConeId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_Cameras_EdgeSettingId
  FK_Table: cam.Cameras
  FK_Column: EdgeSettingId
  Referenced_Table: cam.EdgeSettings
  Referenced_Column: EdgeSettingId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_Cameras_IpSettingId
  FK_Table: cam.Cameras
  FK_Column: IpSettingId
  Referenced_Table: cam.IpSettings
  Referenced_Column: IpSettingId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_Cameras_PtzSettingId
  FK_Table: cam.Cameras
  FK_Column: PtzSettingId
  Referenced_Table: cam.PtzSettings
  Referenced_Column: PtzSettingId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_Camera_RecordingServerId
  FK_Table: cam.Cameras
  FK_Column: RecordingServerId
  Referenced_Table: rs.RecordingServers
  Referenced_Column: RecordingServerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_Cameras_RecordingSettingId
  FK_Table: cam.Cameras
  FK_Column: RecordingSettingId
  Referenced_Table: cam.RecordingSettings
  Referenced_Column: RecordingSettingId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_Cameras_StreamProcessingId
  FK_Table: cam.Cameras
  FK_Column: StreamProcessingId
  Referenced_Table: cam.StreamProcessings
  Referenced_Column: StreamProcessingId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_Cameras_StretchSettingId
  FK_Table: cam.Cameras
  FK_Column: StretchSettingId
  Referenced_Table: cam.StretchSettings
  Referenced_Column: StretchSettingId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_Camera_VideoDeviceId
  FK_Table: cam.Cameras
  FK_Column: VideoDeviceId
  Referenced_Table: rsd.VideoDevices
  Referenced_Column: VideoDeviceId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_CameraVolume_CameraId
  FK_Table: cam.CameraVolumes
  FK_Column: CameraId
  Referenced_Table: cam.Cameras
  Referenced_Column: CameraId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_CameraVolume_VolumeId
  FK_Table: cam.CameraVolumes
  FK_Column: VolumeId
  Referenced_Table: rs.Volumes
  Referenced_Column: VolumeId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_CamEvents_CameraEventId
  FK_Table: cam.CamEvents
  FK_Column: CameraEventId
  Referenced_Table: rsd.CameraEvents
  Referenced_Column: CameraEventId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_CamEvents_CameraId
  FK_Table: cam.CamEvents
  FK_Column: CameraId
  Referenced_Table: cam.Cameras
  Referenced_Column: CameraId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_MediaStreams_AudioDeviceId
  FK_Table: cam.MediaStreams
  FK_Column: AudioDeviceId
  Referenced_Table: rsd.AudioDevices
  Referenced_Column: AudioDeviceId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_MediaStreams_CameraId
  FK_Table: cam.MediaStreams
  FK_Column: CameraId
  Referenced_Table: cam.Cameras
  Referenced_Column: CameraId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_MotionZones_CameraId
  FK_Table: cam.MotionZones
  FK_Column: CameraId
  Referenced_Table: cam.Cameras
  Referenced_Column: CameraId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_Presets_PtzSettingId
  FK_Table: cam.Presets
  FK_Column: PtzSettingId
  Referenced_Table: cam.PtzSettings
  Referenced_Column: PtzSettingId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_ProCamps_MediaStreamId
  FK_Table: cam.ProCamps
  FK_Column: MediaStreamId
  Referenced_Table: cam.MediaStreams
  Referenced_Column: MediaStreamId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_Retention_CameraId
  FK_Table: cam.RetentionPolicies
  FK_Column: CameraId
  Referenced_Table: cam.Cameras
  Referenced_Column: CameraId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_VideoOverlays_StreamProcessingId
  FK_Table: cam.VideoOverlays
  FK_Column: StreamProcessingId
  Referenced_Table: cam.StreamProcessings
  Referenced_Column: StreamProcessingId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_RegionMembers_RecordingServerId
  FK_Table: cfg.RegionMembers
  FK_Column: RecordingServerId
  Referenced_Table: rs.RecordingServers
  Referenced_Column: RecordingServerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_RegionMembers_RegionHierarchyId
  FK_Table: cfg.RegionMembers
  FK_Column: RegionHierarchyId
  Referenced_Table: cfg.RegionsHierarchy
  Referenced_Column: RegionHierarchyId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_RegionMembers_ViewId
  FK_Table: cfg.RegionMembers
  FK_Column: ViewId
  Referenced_Table: vw.Views
  Referenced_Column: ViewId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_RegionsHierarchy_ParentId
  FK_Table: cfg.RegionsHierarchy
  FK_Column: ParentId
  Referenced_Table: cfg.RegionsHierarchy
  Referenced_Column: RegionHierarchyId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_RegionsHierarchy_RegionTypeId
  FK_Table: cfg.RegionsHierarchy
  FK_Column: RegionTypeId
  Referenced_Table: cfg.RegionTypes
  Referenced_Column: RegionTypeId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_Certificate_LocalSecureWebServerId
  FK_Table: com.Certificates
  FK_Column: LocalSecureWebServerId
  Referenced_Table: com.LocalSecureWebServers
  Referenced_Column: LocalSecureWebServerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_BandwidthControls_RecordingServerId
  FK_Table: cso.BandwidthControls
  FK_Column: RecordingServerId
  Referenced_Table: rs.RecordingServers
  Referenced_Column: RecordingServerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_GroupMember_GroupId
  FK_Table: grp.GroupMembers
  FK_Column: GroupId
  Referenced_Table: grp.Groups
  Referenced_Column: GroupId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_GroupMember_UserId
  FK_Table: grp.GroupMembers
  FK_Column: UserId
  Referenced_Table: usr.Users
  Referenced_Column: UserId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_Groups_ActiveDirectoryId
  FK_Table: grp.Groups
  FK_Column: ActiveDirectoryId
  Referenced_Table: com.ActiveDirectories
  Referenced_Column: ActiveDirectoryId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_Groups_ConfigurationId
  FK_Table: grp.Groups
  FK_Column: ConfigurationId
  Referenced_Table: usr.Configurations
  Referenced_Column: ConfigurationId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_Groups_ParentId
  FK_Table: grp.Groups
  FK_Column: ParentId
  Referenced_Table: grp.Groups
  Referenced_Column: GroupId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_PushServerStatus_RecordingServerId
  FK_Table: ms.PushServerStatus
  FK_Column: RecordingServerId
  Referenced_Table: rs.RecordingServers
  Referenced_Column: RecordingServerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_PushToServers_RecordingServerId
  FK_Table: ms.PushToServers
  FK_Column: RecordingServerId
  Referenced_Table: rs.RecordingServers
  Referenced_Column: RecordingServerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_DefaultBehaviors_RecordingServerId
  FK_Table: rs.DefaultBehaviors
  FK_Column: RecordingServerId
  Referenced_Table: rs.RecordingServers
  Referenced_Column: RecordingServerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_EventNotificationSetting_EventNotificationId
  FK_Table: rs.EventNotificationSettings
  FK_Column: EventNotificationId
  Referenced_Table: rs.EventNotifications
  Referenced_Column: EventNotificationId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_EventsToLog_RecordingServerId
  FK_Table: rs.EventsToLog
  FK_Column: RecordingServerId
  Referenced_Table: rs.RecordingServers
  Referenced_Column: RecordingServerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_EventTriggerAction_CameraId
  FK_Table: rs.EventTriggerActions
  FK_Column: CameraId
  Referenced_Table: cam.Cameras
  Referenced_Column: CameraId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_EventTriggerAction_EventTriggerId
  FK_Table: rs.EventTriggerActions
  FK_Column: EventTriggerId
  Referenced_Table: rs.EventTriggers
  Referenced_Column: EventTriggerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_EventTriggerAction_IOTriggerId
  FK_Table: rs.EventTriggerActions
  FK_Column: IOTriggerId
  Referenced_Table: rs.IOTriggers
  Referenced_Column: IOTriggerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_EventTriggerAction_VolumeId
  FK_Table: rs.EventTriggerActions
  FK_Column: VolumeId
  Referenced_Table: rs.Volumes
  Referenced_Column: VolumeId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_EventTriggerResponseAction_EventTriggerActionId
  FK_Table: rs.EventTriggerResponseActions
  FK_Column: EventTriggerActionId
  Referenced_Table: rs.EventTriggerActions
  Referenced_Column: EventTriggerActionId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_EventTriggerResponseAction_EventTriggerId
  FK_Table: rs.EventTriggerResponseActions
  FK_Column: EventTriggerId
  Referenced_Table: rs.EventTriggers
  Referenced_Column: EventTriggerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_EventTriggerResponseAction_EventTriggerResponseId
  FK_Table: rs.EventTriggerResponseActions
  FK_Column: EventTriggerResponseId
  Referenced_Table: rs.EventTriggerResponses
  Referenced_Column: EventTriggerResponseId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_EventTrigger_EventTriggerResponseId
  FK_Table: rs.EventTriggers
  FK_Column: EventTriggerResponseId
  Referenced_Table: rs.EventTriggerResponses
  Referenced_Column: EventTriggerResponseId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_EventTrigger_RecordingServerId
  FK_Table: rs.EventTriggers
  FK_Column: RecordingServerId
  Referenced_Table: rs.RecordingServers
  Referenced_Column: RecordingServerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_EventTriggerSource_CameraId
  FK_Table: rs.EventTriggerSources
  FK_Column: CameraId
  Referenced_Table: cam.Cameras
  Referenced_Column: CameraId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_EventTriggerSource_EventTriggerId
  FK_Table: rs.EventTriggerSources
  FK_Column: EventTriggerId
  Referenced_Table: rs.EventTriggers
  Referenced_Column: EventTriggerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_EventTriggerSource_IOTriggerId
  FK_Table: rs.EventTriggerSources
  FK_Column: IOTriggerId
  Referenced_Table: rs.IOTriggers
  Referenced_Column: IOTriggerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_EventTriggerSource_UserId
  FK_Table: rs.EventTriggerSources
  FK_Column: UserId
  Referenced_Table: usr.Users
  Referenced_Column: UserId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_EventTriggerSource_VolumeId
  FK_Table: rs.EventTriggerSources
  FK_Column: VolumeId
  Referenced_Table: rs.Volumes
  Referenced_Column: VolumeId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_IoInput_IOTriggerId
  FK_Table: rs.IoInputs
  FK_Column: IOTriggerId
  Referenced_Table: rs.IOTriggers
  Referenced_Column: IOTriggerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_IoOutputCamera_CameraId
  FK_Table: rs.IoOutputCameras
  FK_Column: CameraId
  Referenced_Table: cam.Cameras
  Referenced_Column: CameraId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_IoOutputCamera_IoOutputId
  FK_Table: rs.IoOutputCameras
  FK_Column: IoOutputId
  Referenced_Table: rs.IoOutputs
  Referenced_Column: IoOutputId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_IoOutputCamera_IoTriggerId
  FK_Table: rs.IoOutputCameras
  FK_Column: IoTriggerId
  Referenced_Table: rs.IOTriggers
  Referenced_Column: IOTriggerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_IoOutput_IOTriggerId
  FK_Table: rs.IoOutputs
  FK_Column: IOTriggerId
  Referenced_Table: rs.IOTriggers
  Referenced_Column: IOTriggerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_IOTrigger_IODeviceId
  FK_Table: rs.IOTriggers
  FK_Column: IODeviceId
  Referenced_Table: rsd.IODevices
  Referenced_Column: IODeviceId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_IOTrigger_RecordingServerId
  FK_Table: rs.IOTriggers
  FK_Column: RecordingServerId
  Referenced_Table: rs.RecordingServers
  Referenced_Column: RecordingServerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_Logger_LogSettingId
  FK_Table: rs.Loggers
  FK_Column: LogSettingId
  Referenced_Table: rs.LogSettings
  Referenced_Column: LogSettingId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_NVRCamera_CameraId
  FK_Table: rs.NVRCameras
  FK_Column: CameraId
  Referenced_Table: cam.Cameras
  Referenced_Column: CameraId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_NVRCamera_NVRId
  FK_Table: rs.NVRCameras
  FK_Column: NVRId
  Referenced_Table: rs.NVRs
  Referenced_Column: NVRId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_NVR_RecordingServerId
  FK_Table: rs.NVRs
  FK_Column: RecordingServerId
  Referenced_Table: rs.RecordingServers
  Referenced_Column: RecordingServerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_Nvr_VideoDeviceId
  FK_Table: rs.NVRs
  FK_Column: VideoDeviceId
  Referenced_Table: rsd.VideoDevices
  Referenced_Column: VideoDeviceId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_PresetZones_RecordingServerId
  FK_Table: rs.PresetZones
  FK_Column: RecordingServerId
  Referenced_Table: rs.RecordingServers
  Referenced_Column: RecordingServerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_RecordingServer_ConnectionId
  FK_Table: rs.RecordingServers
  FK_Column: ConnectionId
  Referenced_Table: com.Connections
  Referenced_Column: ConnectionId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_RecordingServer_EventNotificationId
  FK_Table: rs.RecordingServers
  FK_Column: EventNotificationId
  Referenced_Table: rs.EventNotifications
  Referenced_Column: EventNotificationId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_RecordingServer_LicenseId
  FK_Table: rs.RecordingServers
  FK_Column: LicenseId
  Referenced_Table: lic.Licenses
  Referenced_Column: LicenseId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_RecordingServer_LocalSecureWebServerId
  FK_Table: rs.RecordingServers
  FK_Column: LocalSecureWebServerId
  Referenced_Table: com.LocalSecureWebServers
  Referenced_Column: LocalSecureWebServerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_RecordingServer_LocalWebServerId
  FK_Table: rs.RecordingServers
  FK_Column: LocalWebServerId
  Referenced_Table: com.LocalWebServers
  Referenced_Column: LocalWebServerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_RecordingServer_LogSettingId
  FK_Table: rs.RecordingServers
  FK_Column: LogSettingId
  Referenced_Table: rs.LogSettings
  Referenced_Column: LogSettingId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_RecordingServer_PortId
  FK_Table: rs.RecordingServers
  FK_Column: PortId
  Referenced_Table: com.Ports
  Referenced_Column: PortId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_RecordingServer_PrimaryRecordingServerFailoverConfigId
  FK_Table: rs.RecordingServers
  FK_Column: PrimaryRecordingServerFailoverConfigId
  Referenced_Table: rsf.PrimaryRecordingServerFailoverConfigs
  Referenced_Column: PrimaryRecordingServerFailoverConfigId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_RecordingServer_StandbyRecordingServerFailoverConfigId
  FK_Table: rs.RecordingServers
  FK_Column: StandbyRecordingServerFailoverConfigId
  Referenced_Table: rsf.StandbyRecordingServerFailoverConfigs
  Referenced_Column: StandbyRecordingServerFailoverConfigId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_RecordingServer_UdpBroadcastId
  FK_Table: rs.RecordingServers
  FK_Column: UdpBroadcastId
  Referenced_Table: rs.UdpBroadcasts
  Referenced_Column: UdpBroadcastId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_VideoDecoder_RecordingServerId
  FK_Table: rs.VideoDecoders
  FK_Column: RecordingServerId
  Referenced_Table: rs.RecordingServers
  Referenced_Column: RecordingServerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_VideoEncoder_RecordingServerId
  FK_Table: rs.VideoEncoders
  FK_Column: RecordingServerId
  Referenced_Table: rs.RecordingServers
  Referenced_Column: RecordingServerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_VolumeExpirationId_VolumeId
  FK_Table: rs.VolumeExpirations
  FK_Column: VolumeId
  Referenced_Table: rs.Volumes
  Referenced_Column: VolumeId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_VolumeExportId_VolumeId
  FK_Table: rs.VolumeExports
  FK_Column: VolumeId
  Referenced_Table: rs.Volumes
  Referenced_Column: VolumeId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_VolumeRetentionId_ArchiveVolumeId
  FK_Table: rs.VolumeRetentions
  FK_Column: ArchiveVolumeId
  Referenced_Table: rs.Volumes
  Referenced_Column: VolumeId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_VolumeRetentionId_BackupVolumeId
  FK_Table: rs.VolumeRetentions
  FK_Column: BackupVolumeId
  Referenced_Table: rs.Volumes
  Referenced_Column: VolumeId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_VolumeRetentionId_VolumeId
  FK_Table: rs.VolumeRetentions
  FK_Column: VolumeId
  Referenced_Table: rs.Volumes
  Referenced_Column: VolumeId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_Volume_RecordingServerId
  FK_Table: rs.Volumes
  FK_Column: RecordingServerId
  Referenced_Table: rs.RecordingServers
  Referenced_Column: RecordingServerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_AudioDeviceServer_AudioDeviceId
  FK_Table: rsd.AudioDeviceServers
  FK_Column: AudioDeviceId
  Referenced_Table: rsd.AudioDevices
  Referenced_Column: AudioDeviceId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_AudioDeviceServer_RecordingServerId
  FK_Table: rsd.AudioDeviceServers
  FK_Column: RecordingServerId
  Referenced_Table: rs.RecordingServers
  Referenced_Column: RecordingServerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_CameraEvent_VideoDeviceId
  FK_Table: rsd.CameraEvents
  FK_Column: VideoDeviceId
  Referenced_Table: rsd.VideoDevices
  Referenced_Column: VideoDeviceId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_DeviceTrigger_VideoDeviceId
  FK_Table: rsd.DeviceTriggers
  FK_Column: VideoDeviceId
  Referenced_Table: rsd.VideoDevices
  Referenced_Column: VideoDeviceId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_DiskDeviceServer_DiskDeviceId
  FK_Table: rsd.DiskDeviceServers
  FK_Column: DiskDeviceId
  Referenced_Table: rsd.DiskDevices
  Referenced_Column: DiskDeviceId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_DiskDeviceServer_RecordingServerId
  FK_Table: rsd.DiskDeviceServers
  FK_Column: RecordingServerId
  Referenced_Table: rs.RecordingServers
  Referenced_Column: RecordingServerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_IODeviceServer_IODeviceId
  FK_Table: rsd.IODeviceServers
  FK_Column: IODeviceId
  Referenced_Table: rsd.IODevices
  Referenced_Column: IODeviceId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_IODeviceServer_RecordingServerId
  FK_Table: rsd.IODeviceServers
  FK_Column: RecordingServerId
  Referenced_Table: rs.RecordingServers
  Referenced_Column: RecordingServerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_MediaProfile_VideoDeviceId
  FK_Table: rsd.MediaProfiles
  FK_Column: VideoDeviceId
  Referenced_Table: rsd.VideoDevices
  Referenced_Column: VideoDeviceId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_SerialDeviceServer_RecordingServerId
  FK_Table: rsd.SerialDeviceServers
  FK_Column: RecordingServerId
  Referenced_Table: rs.RecordingServers
  Referenced_Column: RecordingServerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_SerialDeviceServer_SerialDeviceId
  FK_Table: rsd.SerialDeviceServers
  FK_Column: SerialDeviceId
  Referenced_Table: rsd.SerialDevices
  Referenced_Column: SerialDeviceId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_VideoDevices_ProviderServerId
  FK_Table: rsd.VideoDevices
  FK_Column: ProviderServerId
  Referenced_Table: rs.RecordingServers
  Referenced_Column: RecordingServerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_VideoDeviceServer_RecordingServerId
  FK_Table: rsd.VideoDeviceServers
  FK_Column: RecordingServerId
  Referenced_Table: rs.RecordingServers
  Referenced_Column: RecordingServerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_VideoDeviceServer_VideoDeviceId
  FK_Table: rsd.VideoDeviceServers
  FK_Column: VideoDeviceId
  Referenced_Table: rsd.VideoDevices
  Referenced_Column: VideoDeviceId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_PrimariesStandbyServers_PrimaryRecordingServerId
  FK_Table: rsf.PrimariesStandbyServers
  FK_Column: PrimaryRecordingServerId
  Referenced_Table: rs.RecordingServers
  Referenced_Column: RecordingServerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_PrimariesStandbyServers_StandbyRecordingServerId
  FK_Table: rsf.PrimariesStandbyServers
  FK_Column: StandbyRecordingServerId
  Referenced_Table: rs.RecordingServers
  Referenced_Column: RecordingServerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_StandbyRecordingServerFailoverConfigs_PrimaryRecordingServerId
  FK_Table: rsf.StandbyRecordingServerFailoverConfigs
  FK_Column: PrimaryRecordingServerId
  Referenced_Table: rs.RecordingServers
  Referenced_Column: RecordingServerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_AgentVis_RecordingServerId
  FK_Table: rsi.AgentVis
  FK_Column: RecordingServerId
  Referenced_Table: rs.RecordingServers
  Referenced_Column: RecordingServerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_AxisOneClicks_RecordingServerId
  FK_Table: rsi.AxisOneClicks
  FK_Column: RecordingServerId
  Referenced_Table: rs.RecordingServers
  Referenced_Column: RecordingServerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_Bolds_RecordingServerId
  FK_Table: rsi.Bolds
  FK_Column: RecordingServerId
  Referenced_Table: rs.RecordingServers
  Referenced_Column: RecordingServerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_BriefCams_RecordingServerId
  FK_Table: rsi.BriefCams
  FK_Column: RecordingServerId
  Referenced_Table: rs.RecordingServers
  Referenced_Column: RecordingServerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_S2s_RecordingServerId
  FK_Table: rsi.S2s
  FK_Column: RecordingServerId
  Referenced_Table: rs.RecordingServers
  Referenced_Column: RecordingServerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_SureViews_RecordingServerId
  FK_Table: rsi.SureViews
  FK_Column: RecordingServerId
  Referenced_Table: rs.RecordingServers
  Referenced_Column: RecordingServerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_StorageDrive_RecordingServerId
  FK_Table: rsp.StorageDrives
  FK_Column: RecordingServerId
  Referenced_Table: rs.RecordingServers
  Referenced_Column: RecordingServerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_StoragePool_RecordingServerId
  FK_Table: rsp.StoragePools
  FK_Column: RecordingServerId
  Referenced_Table: rs.RecordingServers
  Referenced_Column: RecordingServerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_StoragePoolSettings_RecordingServerId
  FK_Table: rsp.StoragePoolSettings
  FK_Column: RecordingServerId
  Referenced_Table: rs.RecordingServers
  Referenced_Column: RecordingServerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_CameraSchedule_CameraId
  FK_Table: shl.CameraSchedules
  FK_Column: CameraId
  Referenced_Table: cam.Cameras
  Referenced_Column: CameraId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_CameraSchedule_ScheduleId
  FK_Table: shl.CameraSchedules
  FK_Column: ScheduleId
  Referenced_Table: shl.Schedules
  Referenced_Column: ScheduleId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_EventTriggerSchedule_EventTriggerId
  FK_Table: shl.EventTriggerSchedules
  FK_Column: EventTriggerId
  Referenced_Table: rs.EventTriggers
  Referenced_Column: EventTriggerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_EventTriggerSchedule_ScheduleId
  FK_Table: shl.EventTriggerSchedules
  FK_Column: ScheduleId
  Referenced_Table: shl.Schedules
  Referenced_Column: ScheduleId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_ScheduledimeSegment_ScheduleId
  FK_Table: shl.ScheduledTimeSegments
  FK_Column: ScheduleId
  Referenced_Table: shl.Schedules
  Referenced_Column: ScheduleId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_ScheduledimeSegment_ScheduleOperationId
  FK_Table: shl.ScheduledTimeSegments
  FK_Column: ScheduleOperationId
  Referenced_Table: shl.ScheduleOperations
  Referenced_Column: ScheduleOperationId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_ScheduleOperation_ScheduleId
  FK_Table: shl.ScheduleOperations
  FK_Column: ScheduleId
  Referenced_Table: shl.Schedules
  Referenced_Column: ScheduleId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_Schedule_RecordingServerId
  FK_Table: shl.Schedules
  FK_Column: RecordingServerId
  Referenced_Table: rs.RecordingServers
  Referenced_Column: RecordingServerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_PasswordHistory_UserId
  FK_Table: usr.PasswordHistory
  FK_Column: UserId
  Referenced_Table: usr.Users
  Referenced_Column: UserId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_UserCameras_CameraId
  FK_Table: usr.UserCameras
  FK_Column: CameraId
  Referenced_Table: cam.Cameras
  Referenced_Column: CameraId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_UserCameras_ConfigurationId
  FK_Table: usr.UserCameras
  FK_Column: ConfigurationId
  Referenced_Table: usr.Configurations
  Referenced_Column: ConfigurationId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_UserEvents_ConfigurationId
  FK_Table: usr.UserEvents
  FK_Column: ConfigurationId
  Referenced_Table: usr.Configurations
  Referenced_Column: ConfigurationId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_UserEventTriggers_ConfigurationId
  FK_Table: usr.UserEventTriggers
  FK_Column: ConfigurationId
  Referenced_Table: usr.Configurations
  Referenced_Column: ConfigurationId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_UserEventTriggers_EventTriggerId
  FK_Table: usr.UserEventTriggers
  FK_Column: EventTriggerId
  Referenced_Table: rs.EventTriggers
  Referenced_Column: EventTriggerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_UserInputs_ConfigurationId
  FK_Table: usr.UserInputs
  FK_Column: ConfigurationId
  Referenced_Table: usr.Configurations
  Referenced_Column: ConfigurationId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_UserInputs_IOInputId
  FK_Table: usr.UserInputs
  FK_Column: IOInputId
  Referenced_Table: rs.IoInputs
  Referenced_Column: IoInputId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_UserModules_ConfigurationId
  FK_Table: usr.UserModules
  FK_Column: ConfigurationId
  Referenced_Table: usr.Configurations
  Referenced_Column: ConfigurationId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_UserOutputs_ConfigurationId
  FK_Table: usr.UserOutputs
  FK_Column: ConfigurationId
  Referenced_Table: usr.Configurations
  Referenced_Column: ConfigurationId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_UserOutputs_IOOutputId
  FK_Table: usr.UserOutputs
  FK_Column: IOOutputId
  Referenced_Table: rs.IoOutputs
  Referenced_Column: IoOutputId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_Users_UserId
  FK_Table: usr.UserPasswordPolicies
  FK_Column: UserId
  Referenced_Table: usr.Users
  Referenced_Column: UserId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_UserPresets_CameraId
  FK_Table: usr.UserPresets
  FK_Column: CameraId
  Referenced_Table: cam.Cameras
  Referenced_Column: CameraId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_UserPresets_ConfigurationId
  FK_Table: usr.UserPresets
  FK_Column: ConfigurationId
  Referenced_Table: usr.Configurations
  Referenced_Column: ConfigurationId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_UserRoles_ConfigurationId
  FK_Table: usr.UserRoles
  FK_Column: ConfigurationId
  Referenced_Table: usr.Configurations
  Referenced_Column: ConfigurationId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_UserRoles_RoleId
  FK_Table: usr.UserRoles
  FK_Column: RoleId
  Referenced_Table: usr.Roles
  Referenced_Column: RoleId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_Users_ActiveDirectoryId
  FK_Table: usr.Users
  FK_Column: ActiveDirectoryId
  Referenced_Table: com.ActiveDirectories
  Referenced_Column: ActiveDirectoryId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_Users_ConfigurationId
  FK_Table: usr.Users
  FK_Column: ConfigurationId
  Referenced_Table: usr.Configurations
  Referenced_Column: ConfigurationId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_UserServers_ConfigurationId
  FK_Table: usr.UserServers
  FK_Column: ConfigurationId
  Referenced_Table: usr.Configurations
  Referenced_Column: ConfigurationId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_UserServers_RecordingServerId
  FK_Table: usr.UserServers
  FK_Column: RecordingServerId
  Referenced_Table: rs.RecordingServers
  Referenced_Column: RecordingServerId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_UserViews_ConfigurationId
  FK_Table: usr.UserViews
  FK_Column: ConfigurationId
  Referenced_Table: usr.Configurations
  Referenced_Column: ConfigurationId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_UserViews_ViewId
  FK_Table: usr.UserViews
  FK_Column: ViewId
  Referenced_Table: vw.Views
  Referenced_Column: ViewId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_CameraView_CameraId
  FK_Table: vw.CameraViews
  FK_Column: CameraId
  Referenced_Table: cam.Cameras
  Referenced_Column: CameraId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_CameraView_ViewId
  FK_Table: vw.CameraViews
  FK_Column: ViewId
  Referenced_Table: vw.Views
  Referenced_Column: ViewId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_CameraViewTrigger_CameraViewId
  FK_Table: vw.CameraViewTriggers
  FK_Column: CameraViewId
  Referenced_Table: vw.CameraViews
  Referenced_Column: CameraViewId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_CameraViewTrigger_IoOutputId
  FK_Table: vw.CameraViewTriggers
  FK_Column: IoOutputId
  Referenced_Table: rs.IoOutputs
  Referenced_Column: IoOutputId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_CameraViewTrigger_ViewId
  FK_Table: vw.CameraViewTriggers
  FK_Column: ViewId
  Referenced_Table: vw.Views
  Referenced_Column: ViewId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_FixedTile_TileId
  FK_Table: vw.FixedTiles
  FK_Column: TileId
  Referenced_Table: vw.Tiles
  Referenced_Column: TileId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_FixedTile_ViewId
  FK_Table: vw.FixedTiles
  FK_Column: ViewId
  Referenced_Table: vw.Views
  Referenced_Column: ViewId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_Map_MapImageId
  FK_Table: vw.Maps
  FK_Column: MapImageId
  Referenced_Table: vw.MapImages
  Referenced_Column: MapImageId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_MapViewCamera_CameraId
  FK_Table: vw.MapViewCameras
  FK_Column: CameraId
  Referenced_Table: cam.Cameras
  Referenced_Column: CameraId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_MapViewCamera_MapViewId
  FK_Table: vw.MapViewCameras
  FK_Column: MapViewId
  Referenced_Table: vw.MapViews
  Referenced_Column: MapViewId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_MapViewCamera_ViewId
  FK_Table: vw.MapViewCameras
  FK_Column: ViewId
  Referenced_Table: vw.Views
  Referenced_Column: ViewId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_MapViewLink_LinkId
  FK_Table: vw.MapViewLinks
  FK_Column: LinkedViewId
  Referenced_Table: vw.Views
  Referenced_Column: ViewId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_MapViewLink_MapViewId
  FK_Table: vw.MapViewLinks
  FK_Column: MapViewId
  Referenced_Table: vw.MapViews
  Referenced_Column: MapViewId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_MapViewLink_ViewId
  FK_Table: vw.MapViewLinks
  FK_Column: ViewId
  Referenced_Table: vw.Views
  Referenced_Column: ViewId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_MapView_MapId
  FK_Table: vw.MapViews
  FK_Column: MapId
  Referenced_Table: vw.Maps
  Referenced_Column: MapId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_MapView_ViewId
  FK_Table: vw.MapViews
  FK_Column: ViewId
  Referenced_Table: vw.Views
  Referenced_Column: ViewId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_Pin_MapViewId
  FK_Table: vw.Pins
  FK_Column: MapViewId
  Referenced_Table: vw.MapViews
  Referenced_Column: MapViewId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_Pin_ViewId
  FK_Table: vw.Pins
  FK_Column: ViewId
  Referenced_Table: vw.Views
  Referenced_Column: ViewId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_ResponsiveTile_ParentTileId
  FK_Table: vw.ResponsiveTiles
  FK_Column: ParentTileId
  Referenced_Table: vw.ResponsiveTiles
  Referenced_Column: ResponsiveTileId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_ResponsiveTile_TileId
  FK_Table: vw.ResponsiveTiles
  FK_Column: TileId
  Referenced_Table: vw.Tiles
  Referenced_Column: TileId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_ResponsiveTile_ViewId
  FK_Table: vw.ResponsiveTiles
  FK_Column: ViewId
  Referenced_Table: vw.Views
  Referenced_Column: ViewId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_Tile_CameraViewId
  FK_Table: vw.Tiles
  FK_Column: CameraViewId
  Referenced_Table: vw.CameraViews
  Referenced_Column: CameraViewId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_Tile_MapViewId
  FK_Table: vw.Tiles
  FK_Column: MapViewId
  Referenced_Table: vw.MapViews
  Referenced_Column: MapViewId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_Tile_WallViewId
  FK_Table: vw.Tiles
  FK_Column: WallViewId
  Referenced_Table: vw.WallViews
  Referenced_Column: WallViewId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_Tile_WebViewId
  FK_Table: vw.Tiles
  FK_Column: WebViewId
  Referenced_Table: vw.WebViews
  Referenced_Column: WebViewId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_View_CreatedByUserId
  FK_Table: vw.Views
  FK_Column: CreatedByUserId
  Referenced_Table: usr.Users
  Referenced_Column: UserId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_View_RelatedViewId
  FK_Table: vw.Views
  FK_Column: RelatedViewId
  Referenced_Table: vw.Views
  Referenced_Column: ViewId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_WallView_ViewId
  FK_Table: vw.WallViews
  FK_Column: ViewId
  Referenced_Table: vw.Views
  Referenced_Column: ViewId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_WallViews_WvAgentDisplayId
  FK_Table: vw.WallViews
  FK_Column: WvAgentDisplayId
  Referenced_Table: wall.WvAgentDisplays
  Referenced_Column: WvAgentDisplayId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_WebView_ViewId
  FK_Table: vw.WebViews
  FK_Column: ViewId
  Referenced_Table: vw.Views
  Referenced_Column: ViewId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_TemplateTile_ParentTemplateTileId
  FK_Table: vwt.TemplateTiles
  FK_Column: ParentTemplateTileId
  Referenced_Table: vwt.TemplateTiles
  Referenced_Column: TemplateTileId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_TemplateTile_ViewTemplateId
  FK_Table: vwt.TemplateTiles
  FK_Column: ViewTemplateId
  Referenced_Table: vwt.ViewTemplates
  Referenced_Column: ViewTemplateId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION

  FK_ConstraintName: FK_WvAgentDisplays_WallViewAgentId
  FK_Table: wall.WvAgentDisplays
  FK_Column: WallViewAgentId
  Referenced_Table: wall.WallViewAgents
  Referenced_Column: WallViewAgentId
  Delete_Action: NO_ACTION
  Update_Action: NO_ACTION


ENHANCED INDEX INFORMATION:
--------------------------
  No data found


CHECK CONSTRAINTS:
-----------------
  No data found


VIEWS AND DEFINITIONS:
---------------------
  No data found


STORED PROCEDURES AND FUNCTIONS:
-------------------------------
  No data found


TRIGGERS:
--------
  No data found


USER DEFINED DATA TYPES:
-----------------------
  No data found


EXTENDED PROPERTIES:
-------------------
  No data found


TABLE STATISTICS:
----------------
  No data found


OBJECT DEPENDENCIES:
-------------------
  ReferencingObject: cfg.RegionMembers
  ReferencingType: USER_TABLE
  ReferencedObject: cfg.RegionMembers
  ReferencedType: USER_TABLE

  ReferencingObject: usr.Users
  ReferencingType: USER_TABLE
  ReferencedObject: usr.Users
  ReferencedType: USER_TABLE


DATABASE INFORMATION:
--------------------
  DatabaseName: CompleteViewVms
  SQLServerVersion: 15.0.2130.3
  SQLServerEdition: Express Edition (64-bit)
  ServerCollation: SQL_Latin1_General_CP1_CI_AS
  DatabaseCollation: SQL_Latin1_General_CP1_CI_AS
  DatabaseCreated: 2023-12-18 17:32:37.547000
  CompatibilityLevel: 150


TABLE DETAILS WITH SAMPLE DATA:
========================================

Table: com.ActiveDirectories
----------------------------
Columns:
  ActiveDirectoryId (int)
  IsEnabled (bit)
  Domain (nvarchar)
  BaseDn (nvarchar)
  Username (nvarchar)
  Password (nvarchar)
  SearchNestedDomains (bit)
  SearchNestedGroups (bit)
  GroupReauthIntervalSecs (int)


Table: cam.AdvancedSettings
---------------------------
Columns:
  AdvancedSettingId (int)
  VideoEncoderType (int)
  VideoDecoderType (int)


Table: ms.BackupConfigs
-----------------------
Columns:
  BackupConfigId (int)
  IsEnabled (bit)
  IsDefault (bit)
  Name (nvarchar)
  CronExpression (nvarchar)
  BackupPath (nvarchar)
  BackupTimestampFormat (nvarchar)
  BackupOptions (nvarchar)


Table: cso.BandwidthControls
----------------------------
Columns:
  BandwidthControlId (int)
  RecordingServerId (int)
  IsOnDemandEventsChecked (bit)
  StatusPeriodMs (int)


Table: vw.CameraViewTriggers
----------------------------
Columns:
  CameraViewTriggerId (int)
  ViewId (int)
  CameraViewId (int)
  IoOutputId (int)


Table: ms.DicoveredServers
--------------------------
Columns:
  DicoveredServerId (int)
  IpAddress (nvarchar)
  FriendlyName (nvarchar)
  ServerGuid (uniqueidentifier)
  LastUpdated (datetime)


Table: com.EmailServers
-----------------------
Columns:
  EmailServerId (int)
  IsEnabled (bit)
  SmtpHostAddress (nvarchar)
  SmtpHostPort (int)
  Username (nvarchar)
  Password (nvarchar)
  WillUseTls (bit)
  WillAuthenticate (bit)
  SenderName (nvarchar)
  SenderEmail (nvarchar)


Table: rs.EventTriggerActions
-----------------------------
Columns:
  EventTriggerActionId (int)
  EventTriggerId (int)
  EventTriggerActionGuid (uniqueidentifier)
  ActionType (int)
  ActionValue (int)
  PriorityType (int)
  IsPrimaryAction (bit)
  CameraId (int)
  IOTriggerId (int)
  VolumeId (int)


Table: rs.EventTriggerResponseActions
-------------------------------------
Columns:
  EventTriggerResponseActionId (int)
  EventTriggerId (int)
  EventTriggerResponseId (int)
  EventTriggerActionId (int)


Table: rs.EventTriggerResponses
-------------------------------
Columns:
  EventTriggerResponseId (int)
  EventTriggerId (int)
  IsAckRequired (bit)
  IsResetRequired (bit)
  CanForward (bit)


Table: rs.EventTriggers
-----------------------
Columns:
  EventTriggerId (int)
  RecordingServerId (int)
  EventTriggerResponseId (int)
  EventTriggerGuid (uniqueidentifier)
  IsEnabled (bit)
  Name (nvarchar)
  SetIOTrigger (bit)
  PriorityType (int)
  Note (nvarchar)


Table: shl.EventTriggerSchedules
--------------------------------
Columns:
  EventTriggerScheduleId (int)
  ScheduleId (int)
  EventTriggerId (int)


Table: rs.EventTriggerSources
-----------------------------
Columns:
  EventTriggerSourceId (int)
  EventTriggerId (int)
  EventSourceGuid (uniqueidentifier)
  EventType (int)
  EventValue (int)
  IsPrimaryEvent (bit)
  CameraId (int)
  IOTriggerId (int)
  VolumeId (int)
  UserId (int)


Table: ms.FederatedChildren
---------------------------
Columns:
  Guid (uniqueidentifier)
  Name (nvarchar)
  Version (nvarchar)
  UseTls (bit)
  HostAddress (nvarchar)
  Port (int)
  Identity (nvarchar)
  Password (nvarchar)
  Note (nvarchar)
  WriteToken (uniqueidentifier)
  RecordingServersCount (int)
  CameraLicensesPurchased (int)
  CameraLicensesUsed (int)


Table: ms.FederatedParent
-------------------------
Columns:
  Guid (uniqueidentifier)
  Name (nvarchar)
  Version (nvarchar)
  UseTls (bit)
  HostAddress (nvarchar)
  Port (int)
  Note (nvarchar)
  WriteToken (uniqueidentifier)


Table: ms.FederatedSiblings
---------------------------
Columns:
  Guid (uniqueidentifier)
  Name (nvarchar)
  Version (nvarchar)
  UseTls (bit)
  HostAddress (nvarchar)
  Port (int)


Table: ms.FederationSettings
----------------------------
Columns:
  IsEnabled (bit)


Table: vw.FixedTiles
--------------------
Columns:
  FixedTileId (int)
  ViewId (int)
  TileId (int)
  PositionX (float)
  PositionY (float)
  Width (float)
  Height (float)


Table: ms.ImportRecordingServerQueue
------------------------------------
Columns:
  ImportRecordingServerQueueId (int)
  FriendlyName (nvarchar)
  HostAddress (nvarchar)
  SvrCtrlPortNo (int)
  RestPortNo (int)
  UseTls (bit)
  Username (nvarchar)
  Password (nvarchar)
  AutoProvision (bit)
  ConnectionTimeoutMs (int)
  AddUserId (int)
  EnqueuedAt (datetime)
  RegionHierarchyId (int)
  PostActions (nvarchar)


Table: ms.ImportServerConfigQueue
---------------------------------
Columns:
  ImportServerConfigQueueGuid (uniqueidentifier)
  AutoProvision (bit)
  FriendlyName (nvarchar)
  HostAddress (nvarchar)
  ServerGuid (uniqueidentifier)
  Version (nvarchar)
  UseTls (bit)
  Username (nvarchar)
  Password (nvarchar)
  Platform (nvarchar)
  SerialNo (nvarchar)
  EnqueuedAt (datetime)
  ServerTimeZoneInfo (nvarchar)
  EnvironmentInfo (nvarchar)
  ServerConfigRoot (nvarchar)
  LicenseInfo (nvarchar)

