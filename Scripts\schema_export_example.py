#!/usr/bin/env python3
"""
Example usage of the Comprehensive Schema Export Tool
Shows different ways to configure and use the exporter
"""

import os
from comprehensive_schema_export import ComprehensiveSchemaExporter

def example_basic_usage():
    """Basic usage with default configuration"""
    print("=== Basic Usage Example ===")
    
    # Create exporter with default settings
    exporter = ComprehensiveSchemaExporter()
    
    # Export schema
    schema_data = exporter.export_schema()
    
    # Save in all formats
    files = exporter.save_exports("basic_export")
    
    print(f"Exported {len(schema_data['schema_information'])} schema sections")
    print(f"Created files: {files}")


def example_custom_configuration():
    """Example with custom configuration"""
    print("\n=== Custom Configuration Example ===")
    
    # Custom configuration
    config = {
        'server': 'localhost\\SQLEXPRESS',
        'database': 'TestDB',
        'username': 'testuser',
        'password': 'testpass',
        'output_formats': ['json', 'txt'],  # Only JSON and text
        'sample_size': 5,  # Smaller sample size
        'max_distinct_values': 20,  # Fewer distinct values
        'backup_to_desktop': False  # No desktop backup
    }
    
    exporter = ComprehensiveSchemaExporter(config)
    
    # Export and save
    schema_data = exporter.export_schema()
    files = exporter.save_exports("custom_export")
    
    print(f"Custom export completed: {files}")


def example_environment_variables():
    """Example using environment variables for credentials"""
    print("\n=== Environment Variables Example ===")
    
    # Set environment variables (in practice, set these in your environment)
    os.environ['DB_SERVER'] = 'REMD-ZS-JV6GJV2\\SQLEXPRESS'
    os.environ['DB_DATABASE'] = 'CompleteViewVms'
    os.environ['DB_USERNAME'] = 'sa'
    os.environ['DB_PASSWORD'] = 'Zipper091584!'
    
    # Exporter will automatically use environment variables
    exporter = ComprehensiveSchemaExporter()
    
    print(f"Using server: {exporter.config['server']}")
    print(f"Using database: {exporter.config['database']}")
    
    # Export
    schema_data = exporter.export_schema()
    files = exporter.save_exports("env_export")
    
    print(f"Environment-based export completed: {files}")


def example_json_only_export():
    """Example for AI consumption - JSON only"""
    print("\n=== AI-Optimized JSON Export ===")
    
    config = {
        'output_formats': ['json'],  # JSON only for AI
        'sample_size': 20,  # More sample data
        'max_distinct_values': 100,  # More distinct values
        'backup_to_desktop': True
    }
    
    exporter = ComprehensiveSchemaExporter(config)
    schema_data = exporter.export_schema()
    files = exporter.save_exports("ai_optimized_schema")
    
    print(f"AI-optimized export completed: {files}")
    print(f"Schema contains {len(schema_data['table_details'])} detailed tables")
    
    # Show sample of what AI gets
    if schema_data['table_details']:
        first_table = list(schema_data['table_details'].keys())[0]
        table_info = schema_data['table_details'][first_table]
        print(f"\nSample table info for AI: {first_table}")
        print(f"  - Columns: {len(table_info['columns'])}")
        print(f"  - Sample rows: {len(table_info['sample_data'])}")
        
        # Show first column details
        if table_info['columns']:
            first_col = list(table_info['columns'].keys())[0]
            col_info = table_info['columns'][first_col]
            print(f"  - First column '{first_col}': {col_info['data_type']}")
            if col_info['statistics']:
                stats = col_info['statistics']
                print(f"    * Null percentage: {stats.get('null_percentage', 'N/A')}%")
                print(f"    * Distinct values: {stats.get('distinct_count', 'N/A')}")


if __name__ == "__main__":
    try:
        # Run all examples
        example_basic_usage()
        example_custom_configuration()
        example_environment_variables()
        example_json_only_export()
        
        print("\n" + "=" * 60)
        print("All examples completed successfully!")
        print("Check the generated files for comprehensive schema information.")
        
    except Exception as e:
        print(f"Example failed: {e}")
        print("Make sure the database connection details are correct.")
