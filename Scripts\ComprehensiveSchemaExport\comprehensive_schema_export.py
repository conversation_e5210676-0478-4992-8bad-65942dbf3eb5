#!/usr/bin/env python3
"""
Comprehensive SQL Server Schema Export Tool
Exports complete database schema information for AI agent consumption
Includes tables, relationships, constraints, procedures, data samples, and metadata
"""

import pyodbc
import json
import xml.etree.ElementTree as ET
import csv
import os
import sys
import socket
import time
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from contextlib import contextmanager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('schema_export.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class DatabaseConnectionError(Exception):
    """Custom exception for database connection issues"""
    pass

class SchemaExportError(Exception):
    """Custom exception for schema export issues"""
    pass

class ComprehensiveSchemaExporter:
    """Comprehensive database schema exporter with AI-friendly output"""
    
    def __init__(self, config: Optional[Dict] = None):
        """Initialize the schema exporter with configuration"""
        self.config = config or self._get_default_config()
        self.connection = None
        self.schema_data = {}
        self.export_timestamp = datetime.now()
        
    def _get_default_config(self) -> Dict:
        """Get default configuration with environment variable support"""
        return {
            'server': os.getenv('DB_SERVER', 'REMD-ZS-JV6GJV2\\SQLEXPRESS'),
            'database': os.getenv('DB_DATABASE', 'CompleteViewVms'),
            'username': os.getenv('DB_USERNAME', 'sa'),
            'password': os.getenv('DB_PASSWORD', 'Zipper091584!'),
            'drivers': [
                'ODBC Driver 17 for SQL Server',
                'ODBC Driver 13 for SQL Server',
                'SQL Server Native Client 11.0',
                'SQL Server'
            ],
            'connection_timeout': 30,
            'command_timeout': 300,
            'max_retries': 3,
            'retry_delay': 2,
            'output_formats': ['json', 'xml', 'txt'],
            'sample_size': 10,
            'max_distinct_values': 50,
            'backup_to_desktop': True,
            # Schema filtering configuration
            'excluded_schemas': [
                'sys', 'INFORMATION_SCHEMA', 'guest', 'db_owner', 'db_accessadmin',
                'db_securityadmin', 'db_ddladmin', 'db_backupoperator', 'db_datareader',
                'db_datawriter', 'db_denydatareader', 'db_denydatawriter'
            ],
            'included_schemas': [],  # If specified, only these schemas will be included
            'schema_filter_mode': 'exclude',  # 'exclude', 'include', or 'all'
            # Performance and safety limits for massive tables
            'max_tables_to_sample': 20,  # Maximum number of tables to sample data from
            'max_table_rows_for_sampling': 1000000,  # Skip data sampling for tables larger than this
            'max_table_rows_for_stats': 10000000,  # Skip column statistics for tables larger than this
            'max_table_rows_for_selection': 50000000,  # Exclude tables larger than this from export entirely
            'enable_row_count_limits': True,  # Enable/disable all row count restrictions
            'prioritize_smaller_tables': True,  # Process smaller tables first for better performance
            # AI-focused analysis features
            'include_system_stats': True,        # Include SQL Server system statistics
            'analyze_column_patterns': True,     # Enable pattern analysis
            'detect_table_relationships': True,  # Infer relationships beyond foreign keys
            'generate_ai_hints': True,          # Generate AI-specific metadata
            'export_query_examples': True,      # Generate sample queries for common operations
        }
    
    def _check_dependencies(self) -> bool:
        """Check if required modules are available"""
        try:
            import pyodbc
            logger.info("✓ pyodbc module available")
            return True
        except ImportError as e:
            logger.error(f"✗ Required module missing: {e}")
            logger.error("Install with: pip install pyodbc")
            return False
    
    def _test_tcp_connection(self, server: str, port: int = 1433) -> bool:
        """Test TCP connectivity to SQL Server"""
        try:
            # Extract server name without instance
            server_name = server.split('\\')[0]
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((server_name, port))
            sock.close()
            return result == 0
        except Exception as e:
            logger.warning(f"TCP connection test failed: {e}")
            return False

    def _get_schema_filter_clause(self, table_alias: str = 't') -> str:
        """Generate schema filter clause based on configuration"""
        if self.config['schema_filter_mode'] == 'all':
            return ""

        schema_column = f"SCHEMA_NAME({table_alias}.schema_id)"

        if self.config['schema_filter_mode'] == 'include' and self.config['included_schemas']:
            # Include only specified schemas
            schema_list = "', '".join(self.config['included_schemas'])
            return f"AND {schema_column} IN ('{schema_list}')"

        elif self.config['schema_filter_mode'] == 'exclude' and self.config['excluded_schemas']:
            # Exclude specified schemas
            schema_list = "', '".join(self.config['excluded_schemas'])
            return f"AND {schema_column} NOT IN ('{schema_list}')"

        return ""

    def _get_information_schema_filter(self, schema_column: str = 'TABLE_SCHEMA') -> str:
        """Generate schema filter for INFORMATION_SCHEMA queries"""
        if self.config['schema_filter_mode'] == 'all':
            return ""

        if self.config['schema_filter_mode'] == 'include' and self.config['included_schemas']:
            # Include only specified schemas
            schema_list = "', '".join(self.config['included_schemas'])
            return f"AND {schema_column} IN ('{schema_list}')"

        elif self.config['schema_filter_mode'] == 'exclude' and self.config['excluded_schemas']:
            # Exclude specified schemas
            schema_list = "', '".join(self.config['excluded_schemas'])
            return f"AND {schema_column} NOT IN ('{schema_list}')"

        return ""

    def _get_connection_string(self, driver: str) -> str:
        """Build connection string with specified driver"""
        return (
            f"DRIVER={{{driver}}};"
            f"SERVER={self.config['server']};"
            f"DATABASE={self.config['database']};"
            f"UID={self.config['username']};"
            f"PWD={self.config['password']};"
            f"Connection Timeout={self.config['connection_timeout']};"
        )
    
    @contextmanager
    def _get_connection(self):
        """Get database connection with fallback drivers and retry logic"""
        if not self._check_dependencies():
            raise DatabaseConnectionError("Required dependencies not available")
        
        # Test TCP connectivity first
        if not self._test_tcp_connection(self.config['server']):
            logger.warning("TCP connectivity test failed, but attempting connection anyway...")
        
        connection = None
        last_error = None
        
        for attempt in range(self.config['max_retries']):
            for driver in self.config['drivers']:
                try:
                    conn_str = self._get_connection_string(driver)
                    logger.info(f"Attempting connection with driver: {driver} (attempt {attempt + 1})")
                    
                    connection = pyodbc.connect(conn_str)
                    connection.timeout = self.config['command_timeout']
                    logger.info(f"✓ Connected successfully with {driver}")
                    
                    try:
                        yield connection
                        return
                    finally:
                        if connection:
                            connection.close()
                            logger.info("Database connection closed")
                    
                except pyodbc.Error as e:
                    last_error = e
                    logger.warning(f"✗ Failed with {driver}: {e}")
                    continue
            
            if attempt < self.config['max_retries'] - 1:
                logger.info(f"Retrying in {self.config['retry_delay']} seconds...")
                time.sleep(self.config['retry_delay'])
        
        raise DatabaseConnectionError(f"Failed to connect after {self.config['max_retries']} attempts. Last error: {last_error}")
    
    def _execute_query_safely(self, cursor, query: str, description: str) -> List[Tuple]:
        """Execute query with error handling and progress indication"""
        try:
            logger.info(f"Executing: {description}")
            start_time = time.time()
            cursor.execute(query)
            results = cursor.fetchall()
            elapsed = time.time() - start_time
            logger.info(f"✓ {description} completed in {elapsed:.2f}s ({len(results)} rows)")
            return results
        except Exception as e:
            logger.error(f"✗ Failed to execute {description}: {e}")
            return []
    
    def _get_comprehensive_queries(self) -> Dict[str, str]:
        """Get all comprehensive schema queries with configurable schema filtering"""
        # Get schema filter clauses
        info_schema_filter = self._get_information_schema_filter()
        sys_schema_filter = self._get_schema_filter_clause()

        return {
            # Enhanced table and column information
            "Enhanced Tables and Columns": f"""
                SELECT
                    t.TABLE_SCHEMA + '.' + t.TABLE_NAME AS FullTableName,
                    t.COLUMN_NAME,
                    t.DATA_TYPE,
                    t.IS_NULLABLE,
                    t.COLUMN_DEFAULT,
                    t.CHARACTER_MAXIMUM_LENGTH,
                    t.NUMERIC_PRECISION,
                    t.NUMERIC_SCALE,
                    t.ORDINAL_POSITION,
                    CASE WHEN pk.COLUMN_NAME IS NOT NULL THEN 'YES' ELSE 'NO' END AS IS_PRIMARY_KEY,
                    CASE WHEN fk.COLUMN_NAME IS NOT NULL THEN 'YES' ELSE 'NO' END AS IS_FOREIGN_KEY
                FROM INFORMATION_SCHEMA.COLUMNS t
                LEFT JOIN (
                    SELECT ku.TABLE_SCHEMA, ku.TABLE_NAME, ku.COLUMN_NAME
                    FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
                    JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku ON tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
                    WHERE tc.CONSTRAINT_TYPE = 'PRIMARY KEY' {info_schema_filter.replace('TABLE_SCHEMA', 'ku.TABLE_SCHEMA')}
                ) pk ON t.TABLE_SCHEMA = pk.TABLE_SCHEMA AND t.TABLE_NAME = pk.TABLE_NAME AND t.COLUMN_NAME = pk.COLUMN_NAME
                LEFT JOIN (
                    SELECT ku.TABLE_SCHEMA, ku.TABLE_NAME, ku.COLUMN_NAME
                    FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
                    JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku ON tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
                    WHERE tc.CONSTRAINT_TYPE = 'FOREIGN KEY' {info_schema_filter.replace('TABLE_SCHEMA', 'ku.TABLE_SCHEMA')}
                ) fk ON t.TABLE_SCHEMA = fk.TABLE_SCHEMA AND t.TABLE_NAME = fk.TABLE_NAME AND t.COLUMN_NAME = fk.COLUMN_NAME
                WHERE 1=1 {info_schema_filter.replace('TABLE_SCHEMA', 't.TABLE_SCHEMA')}
                ORDER BY t.TABLE_SCHEMA, t.TABLE_NAME, t.ORDINAL_POSITION;
            """,
            
            # Enhanced foreign key relationships
            "Enhanced Foreign Key Relationships": f"""
                SELECT
                    fk.name AS FK_ConstraintName,
                    SCHEMA_NAME(tp.schema_id) + '.' + tp.name AS FK_Table,
                    cp.name AS FK_Column,
                    SCHEMA_NAME(tr.schema_id) + '.' + tr.name AS Referenced_Table,
                    cr.name AS Referenced_Column,
                    fk.delete_referential_action_desc AS Delete_Action,
                    fk.update_referential_action_desc AS Update_Action
                FROM sys.foreign_keys fk
                JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
                JOIN sys.tables tp ON fkc.parent_object_id = tp.object_id
                JOIN sys.tables tr ON fkc.referenced_object_id = tr.object_id
                JOIN sys.columns cp ON fkc.parent_object_id = cp.object_id AND fkc.parent_column_id = cp.column_id
                JOIN sys.columns cr ON fkc.referenced_object_id = cr.object_id AND fkc.referenced_column_id = cr.column_id
                WHERE 1=1 {sys_schema_filter.replace('SCHEMA_NAME(t.schema_id)', 'SCHEMA_NAME(tp.schema_id)')}
                ORDER BY FK_Table, FK_Column;
            """,
            
            # Enhanced index information
            "Enhanced Index Information": f"""
                SELECT
                    SCHEMA_NAME(t.schema_id) + '.' + t.name AS TableName,
                    i.name AS IndexName,
                    i.type_desc AS IndexType,
                    i.is_unique AS IsUnique,
                    i.is_primary_key AS IsPrimaryKey,
                    i.is_unique_constraint AS IsUniqueConstraint,
                    STRING_AGG(c.name + CASE WHEN ic.is_descending_key = 1 THEN ' DESC' ELSE ' ASC' END, ', ')
                        WITHIN GROUP (ORDER BY ic.key_ordinal) AS IndexedColumns,
                    i.fill_factor AS FillFactor
                FROM sys.indexes i
                JOIN sys.tables t ON i.object_id = t.object_id
                JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
                JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
                WHERE i.type > 0  -- Exclude heaps
                {sys_schema_filter}
                GROUP BY SCHEMA_NAME(t.schema_id), t.name, i.name, i.type_desc, i.is_unique,
                         i.is_primary_key, i.is_unique_constraint, i.fill_factor
                ORDER BY TableName, IndexName;
            """,

            # Check constraints with definitions
            "Check Constraints": """
                SELECT
                    SCHEMA_NAME(t.schema_id) + '.' + t.name AS TableName,
                    cc.name AS ConstraintName,
                    cc.definition AS CheckDefinition,
                    cc.is_disabled AS IsDisabled
                FROM sys.check_constraints cc
                JOIN sys.tables t ON cc.parent_object_id = t.object_id
                ORDER BY TableName, ConstraintName;
            """,

            # Views with definitions
            "Views and Definitions": f"""
                SELECT
                    SCHEMA_NAME(v.schema_id) + '.' + v.name AS ViewName,
                    m.definition AS ViewDefinition,
                    v.create_date AS CreatedDate,
                    v.modify_date AS ModifiedDate
                FROM sys.views v
                JOIN sys.sql_modules m ON v.object_id = m.object_id
                WHERE 1=1 {sys_schema_filter.replace('t.', 'v.')}
                ORDER BY ViewName;
            """,

            # Stored procedures and functions
            "Stored Procedures and Functions": """
                SELECT
                    SCHEMA_NAME(o.schema_id) + '.' + o.name AS ObjectName,
                    o.type_desc AS ObjectType,
                    o.create_date AS CreatedDate,
                    o.modify_date AS ModifiedDate,
                    m.definition AS ObjectDefinition
                FROM sys.objects o
                JOIN sys.sql_modules m ON o.object_id = m.object_id
                WHERE o.type IN ('P', 'FN', 'TF', 'IF', 'PC')
                ORDER BY o.type_desc, ObjectName;
            """,

            # Triggers
            "Triggers": """
                SELECT
                    SCHEMA_NAME(t.schema_id) + '.' + t.name AS TableName,
                    tr.name AS TriggerName,
                    tr.is_disabled AS IsDisabled,
                    CASE tr.is_instead_of_trigger WHEN 1 THEN 'INSTEAD OF' ELSE 'AFTER' END AS TriggerType,
                    m.definition AS TriggerDefinition
                FROM sys.triggers tr
                JOIN sys.tables t ON tr.parent_id = t.object_id
                JOIN sys.sql_modules m ON tr.object_id = m.object_id
                ORDER BY TableName, TriggerName;
            """,

            # User-defined data types
            "User Defined Data Types": """
                SELECT
                    SCHEMA_NAME(t.schema_id) + '.' + t.name AS TypeName,
                    t.system_type_id,
                    st.name AS BaseTypeName,
                    t.max_length,
                    t.precision,
                    t.scale,
                    t.is_nullable
                FROM sys.types t
                JOIN sys.types st ON t.system_type_id = st.system_type_id
                WHERE t.is_user_defined = 1
                ORDER BY TypeName;
            """,

            # Extended properties (descriptions/comments)
            "Extended Properties": """
                SELECT
                    ep.class_desc AS PropertyClass,
                    CASE ep.class
                        WHEN 1 THEN SCHEMA_NAME(o.schema_id) + '.' + o.name
                        ELSE 'Database'
                    END AS ObjectName,
                    CASE ep.minor_id
                        WHEN 0 THEN 'Object'
                        ELSE COL_NAME(ep.major_id, ep.minor_id)
                    END AS PropertyTarget,
                    ep.name AS PropertyName,
                    ep.value AS PropertyValue
                FROM sys.extended_properties ep
                LEFT JOIN sys.objects o ON ep.major_id = o.object_id
                WHERE ep.name = 'MS_Description'
                ORDER BY ObjectName, PropertyTarget;
            """,

            # Table and column statistics
            "Table Statistics": f"""
                SELECT
                    SCHEMA_NAME(t.schema_id) + '.' + t.name AS TableName,
                    SUM(p.rows) AS RowCount,
                    COUNT(c.column_id) AS ColumnCount,
                    SUM(a.total_pages) * 8 AS TotalSpaceKB,
                    SUM(a.used_pages) * 8 AS UsedSpaceKB,
                    (SUM(a.total_pages) - SUM(a.used_pages)) * 8 AS UnusedSpaceKB
                FROM sys.tables t
                JOIN sys.partitions p ON t.object_id = p.object_id
                JOIN sys.allocation_units a ON p.partition_id = a.container_id
                JOIN sys.columns c ON t.object_id = c.object_id
                WHERE p.index_id IN (0,1) {sys_schema_filter}
                GROUP BY SCHEMA_NAME(t.schema_id), t.name
                ORDER BY RowCount DESC;
            """,

            # Object dependencies
            "Object Dependencies": """
                SELECT
                    SCHEMA_NAME(o1.schema_id) + '.' + o1.name AS ReferencingObject,
                    o1.type_desc AS ReferencingType,
                    SCHEMA_NAME(o2.schema_id) + '.' + o2.name AS ReferencedObject,
                    o2.type_desc AS ReferencedType
                FROM sys.sql_expression_dependencies d
                JOIN sys.objects o1 ON d.referencing_id = o1.object_id
                JOIN sys.objects o2 ON d.referenced_id = o2.object_id
                WHERE d.referenced_id IS NOT NULL
                ORDER BY ReferencingObject, ReferencedObject;
            """,

            # Database information
            "Database Information": """
                SELECT
                    DB_NAME() AS DatabaseName,
                    SERVERPROPERTY('ProductVersion') AS SQLServerVersion,
                    SERVERPROPERTY('Edition') AS SQLServerEdition,
                    SERVERPROPERTY('Collation') AS ServerCollation,
                    d.collation_name AS DatabaseCollation,
                    d.create_date AS DatabaseCreated,
                    d.compatibility_level AS CompatibilityLevel
                FROM sys.databases d
                WHERE d.name = DB_NAME();
            """
        }

        # Add system statistics if enabled
        if self.config.get('include_system_stats', True):
            queries.update(self._get_system_statistics_queries())

        return queries

    def _get_system_statistics_queries(self) -> Dict[str, str]:
        """Get SQL Server system statistics queries"""
        schema_filter = self._get_schema_filter_clause()

        return {
            # Column statistics from SQL Server
            "Column Statistics": f"""
                SELECT
                    SCHEMA_NAME(t.schema_id) + '.' + t.name AS TableName,
                    c.name AS ColumnName,
                    s.name AS StatisticName,
                    st.last_updated AS LastUpdated,
                    st.rows AS SampleRows,
                    st.modification_counter AS ModificationsSinceLastUpdate
                FROM sys.tables t
                JOIN sys.columns c ON t.object_id = c.object_id
                JOIN sys.stats s ON t.object_id = s.object_id
                JOIN sys.stats_columns sc ON s.stats_id = sc.stats_id AND s.object_id = sc.object_id
                CROSS APPLY sys.dm_db_stats_properties(s.object_id, s.stats_id) st
                WHERE c.column_id = sc.column_id {schema_filter}
                ORDER BY TableName, ColumnName;
            """,

            # Index usage statistics
            "Index Usage Statistics": f"""
                SELECT
                    SCHEMA_NAME(t.schema_id) + '.' + t.name AS TableName,
                    i.name AS IndexName,
                    ISNULL(ius.user_seeks, 0) AS UserSeeks,
                    ISNULL(ius.user_scans, 0) AS UserScans,
                    ISNULL(ius.user_lookups, 0) AS UserLookups,
                    ISNULL(ius.user_updates, 0) AS UserUpdates,
                    ius.last_user_seek AS LastUserSeek,
                    ius.last_user_scan AS LastUserScan,
                    ius.last_user_lookup AS LastUserLookup
                FROM sys.indexes i
                JOIN sys.tables t ON i.object_id = t.object_id
                LEFT JOIN sys.dm_db_index_usage_stats ius ON i.object_id = ius.object_id
                    AND i.index_id = ius.index_id AND ius.database_id = DB_ID()
                WHERE i.type > 0 {schema_filter}
                ORDER BY TableName, IndexName;
            """,

            # Table access patterns
            "Table Access Patterns": f"""
                SELECT
                    SCHEMA_NAME(t.schema_id) + '.' + t.name AS TableName,
                    SUM(ISNULL(ius.user_seeks, 0) + ISNULL(ius.user_scans, 0) + ISNULL(ius.user_lookups, 0)) AS TotalReads,
                    SUM(ISNULL(ius.user_updates, 0)) AS TotalWrites,
                    MAX(ius.last_user_seek) AS LastRead,
                    MAX(ius.last_user_update) AS LastWrite
                FROM sys.tables t
                JOIN sys.indexes i ON t.object_id = i.object_id
                LEFT JOIN sys.dm_db_index_usage_stats ius ON i.object_id = ius.object_id
                    AND i.index_id = ius.index_id AND ius.database_id = DB_ID()
                WHERE 1=1 {schema_filter}
                GROUP BY SCHEMA_NAME(t.schema_id), t.name
                HAVING SUM(ISNULL(ius.user_seeks, 0) + ISNULL(ius.user_scans, 0) + ISNULL(ius.user_lookups, 0) + ISNULL(ius.user_updates, 0)) > 0
                ORDER BY TotalReads DESC;
            """
        }

    def _get_table_sample_data(self, cursor, table_name: str, sample_size: int = 10) -> List[Tuple]:
        """Get sample data from a table"""
        try:
            # Use TABLESAMPLE for large tables, TOP for smaller ones
            # Properly bracket table name for safety
            query = f"""
                SELECT TOP {sample_size} *
                FROM [{table_name}]
                ORDER BY (SELECT NULL)  -- Random order
            """
            return self._execute_query_safely(cursor, query, f"Sample data from {table_name}")
        except Exception as e:
            logger.warning(f"Could not get sample data from {table_name}: {e}")
            return []

    def _get_column_statistics(self, cursor, table_name: str, column_name: str) -> Dict:
        """Get statistics for a specific column"""
        try:
            # Get basic statistics with properly bracketed identifiers
            stats_query = f"""
                SELECT
                    COUNT(*) as total_count,
                    COUNT([{column_name}]) as non_null_count,
                    COUNT(DISTINCT [{column_name}]) as distinct_count
                FROM [{table_name}]
            """
            stats_result = self._execute_query_safely(cursor, stats_query, f"Statistics for {table_name}.{column_name}")

            if stats_result:
                total, non_null, distinct = stats_result[0]
                null_count = total - non_null
                null_percentage = (null_count / total * 100) if total > 0 else 0

                return {
                    'total_rows': total,
                    'non_null_count': non_null,
                    'null_count': null_count,
                    'null_percentage': round(null_percentage, 2),
                    'distinct_count': distinct,
                    'uniqueness_ratio': round(distinct / non_null * 100, 2) if non_null > 0 else 0
                }
        except Exception as e:
            logger.warning(f"Could not get statistics for {table_name}.{column_name}: {e}")

        return {}

    def _get_distinct_values(self, cursor, table_name: str, column_name: str, max_values: int = 50) -> List:
        """Get distinct values for categorical columns"""
        try:
            # Properly bracket identifiers for safety with special characters and reserved words
            query = f"""
                SELECT TOP {max_values}
                    [{column_name}],
                    COUNT(*) as frequency
                FROM [{table_name}]
                WHERE [{column_name}] IS NOT NULL
                GROUP BY [{column_name}]
                ORDER BY COUNT(*) DESC
            """
            results = self._execute_query_safely(cursor, query, f"Distinct values for {table_name}.{column_name}")
            return [{'value': row[0], 'frequency': row[1]} for row in results]
        except Exception as e:
            logger.warning(f"Could not get distinct values for {table_name}.{column_name}: {e}")
            return []

    def _analyze_column_patterns(self, cursor, table_name: str, column_name: str, data_type: str) -> Dict:
        """Analyze column patterns for AI insights"""
        if not self.config.get('analyze_column_patterns', True):
            return {}

        try:
            patterns = {}

            # Pattern analysis for string columns
            if data_type.lower() in ['varchar', 'nvarchar', 'char', 'nchar', 'text']:
                pattern_query = f"""
                    SELECT
                        'email_pattern' AS PatternType,
                        COUNT(*) AS MatchCount,
                        CAST(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM [{table_name}] WHERE [{column_name}] IS NOT NULL) AS DECIMAL(5,2)) AS MatchPercentage
                    FROM [{table_name}]
                    WHERE [{column_name}] LIKE '%_@_%.__%'
                    AND [{column_name}] IS NOT NULL

                    UNION ALL

                    SELECT
                        'phone_pattern' AS PatternType,
                        COUNT(*) AS MatchCount,
                        CAST(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM [{table_name}] WHERE [{column_name}] IS NOT NULL) AS DECIMAL(5,2)) AS MatchPercentage
                    FROM [{table_name}]
                    WHERE [{column_name}] LIKE '%[0-9][0-9][0-9]%[0-9][0-9][0-9]%[0-9][0-9][0-9][0-9]%'
                    AND [{column_name}] IS NOT NULL

                    UNION ALL

                    SELECT
                        'url_pattern' AS PatternType,
                        COUNT(*) AS MatchCount,
                        CAST(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM [{table_name}] WHERE [{column_name}] IS NOT NULL) AS DECIMAL(5,2)) AS MatchPercentage
                    FROM [{table_name}]
                    WHERE [{column_name}] LIKE 'http%://%'
                    AND [{column_name}] IS NOT NULL

                    UNION ALL

                    SELECT
                        'guid_pattern' AS PatternType,
                        COUNT(*) AS MatchCount,
                        CAST(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM [{table_name}] WHERE [{column_name}] IS NOT NULL) AS DECIMAL(5,2)) AS MatchPercentage
                    FROM [{table_name}]
                    WHERE [{column_name}] LIKE '[0-9A-F][0-9A-F][0-9A-F][0-9A-F][0-9A-F][0-9A-F][0-9A-F][0-9A-F]-[0-9A-F][0-9A-F][0-9A-F][0-9A-F]-[0-9A-F][0-9A-F][0-9A-F][0-9A-F]-[0-9A-F][0-9A-F][0-9A-F][0-9A-F]-[0-9A-F][0-9A-F][0-9A-F][0-9A-F][0-9A-F][0-9A-F][0-9A-F][0-9A-F][0-9A-F][0-9A-F][0-9A-F][0-9A-F]'
                    AND [{column_name}] IS NOT NULL
                """

                results = self._execute_query_safely(cursor, pattern_query, f"Pattern analysis for {table_name}.{column_name}")
                patterns['data_patterns'] = [{'pattern': row[0], 'count': row[1], 'percentage': row[2]} for row in results if row[1] > 0]

            # Date pattern analysis
            elif data_type.lower() in ['datetime', 'datetime2', 'date', 'smalldatetime']:
                date_query = f"""
                    SELECT
                        'future_dates' AS PatternType,
                        COUNT(*) AS MatchCount
                    FROM [{table_name}]
                    WHERE [{column_name}] > GETDATE()
                    AND [{column_name}] IS NOT NULL

                    UNION ALL

                    SELECT
                        'historical_dates' AS PatternType,
                        COUNT(*) AS MatchCount
                    FROM [{table_name}]
                    WHERE [{column_name}] < DATEADD(YEAR, -10, GETDATE())
                    AND [{column_name}] IS NOT NULL
                """

                results = self._execute_query_safely(cursor, date_query, f"Date pattern analysis for {table_name}.{column_name}")
                patterns['date_patterns'] = [{'pattern': row[0], 'count': row[1]} for row in results]

            return patterns

        except Exception as e:
            logger.warning(f"Could not analyze patterns for {table_name}.{column_name}: {e}")
            return {}

    def _detect_table_relationships(self, cursor) -> Dict[str, List]:
        """Detect potential relationships beyond explicit foreign keys"""
        if not self.config.get('detect_table_relationships', True):
            return {}

        try:
            # Find potential relationships based on naming conventions and data patterns
            relationship_query = f"""
                WITH PotentialRelationships AS (
                    SELECT
                        t1.TABLE_SCHEMA + '.' + t1.TABLE_NAME AS SourceTable,
                        t1.COLUMN_NAME AS SourceColumn,
                        t2.TABLE_SCHEMA + '.' + t2.TABLE_NAME AS TargetTable,
                        t2.COLUMN_NAME AS TargetColumn,
                        'naming_convention' AS RelationshipType,
                        CASE
                            WHEN t1.COLUMN_NAME = t2.COLUMN_NAME THEN 'exact_name_match'
                            WHEN t1.COLUMN_NAME LIKE '%' + t2.TABLE_NAME + '%' THEN 'table_name_in_column'
                            WHEN t1.COLUMN_NAME LIKE '%ID' AND t2.COLUMN_NAME LIKE '%ID' THEN 'id_suffix_match'
                            ELSE 'other'
                        END AS MatchType
                    FROM INFORMATION_SCHEMA.COLUMNS t1
                    JOIN INFORMATION_SCHEMA.COLUMNS t2 ON t1.DATA_TYPE = t2.DATA_TYPE
                    WHERE t1.TABLE_SCHEMA + '.' + t1.TABLE_NAME != t2.TABLE_SCHEMA + '.' + t2.TABLE_NAME
                    AND (
                        t1.COLUMN_NAME = t2.COLUMN_NAME
                        OR t1.COLUMN_NAME LIKE '%' + t2.TABLE_NAME + '%'
                        OR (t1.COLUMN_NAME LIKE '%ID' AND t2.COLUMN_NAME LIKE '%ID')
                    )
                    AND NOT EXISTS (
                        SELECT 1 FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc
                        JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu ON rc.CONSTRAINT_NAME = kcu.CONSTRAINT_NAME
                        WHERE kcu.TABLE_SCHEMA = t1.TABLE_SCHEMA
                        AND kcu.TABLE_NAME = t1.TABLE_NAME
                        AND kcu.COLUMN_NAME = t1.COLUMN_NAME
                    )
                )
                SELECT TOP 50 * FROM PotentialRelationships
                ORDER BY MatchType, SourceTable;
            """

            results = self._execute_query_safely(cursor, relationship_query, "Potential table relationships")

            relationships = {
                'potential_foreign_keys': [
                    {
                        'source_table': row[0],
                        'source_column': row[1],
                        'target_table': row[2],
                        'target_column': row[3],
                        'relationship_type': row[4],
                        'match_type': row[5]
                    }
                    for row in results
                ]
            }

            return relationships

        except Exception as e:
            logger.warning(f"Could not detect table relationships: {e}")
            return {}

    def _generate_ai_hints(self, table_details: Dict) -> Dict[str, Any]:
        """Generate AI-specific metadata and hints"""
        if not self.config.get('generate_ai_hints', True):
            return {}

        try:
            ai_hints = {
                'table_classifications': {},
                'column_semantics': {},
                'query_patterns': {},
                'business_rules': {}
            }

            for table_name, details in table_details.items():
                # Classify table type based on naming and structure
                table_classification = self._classify_table_type(table_name, details)
                ai_hints['table_classifications'][table_name] = table_classification

                # Analyze column semantics
                column_semantics = {}
                for col_name, col_info in details.get('columns', {}).items():
                    semantics = self._analyze_column_semantics(col_name, col_info)
                    if semantics:
                        column_semantics[col_name] = semantics

                ai_hints['column_semantics'][table_name] = column_semantics

                # Suggest query patterns
                query_patterns = self._suggest_query_patterns(table_name, details)
                ai_hints['query_patterns'][table_name] = query_patterns

            return ai_hints

        except Exception as e:
            logger.warning(f"Could not generate AI hints: {e}")
            return {}

    def _classify_table_type(self, table_name: str, details: Dict) -> Dict[str, str]:
        """Classify table type for AI understanding"""
        classification = {'type': 'unknown', 'confidence': 'low', 'reasoning': []}

        table_lower = table_name.lower()
        columns = details.get('columns', {})
        column_names = [col.lower() for col in columns.keys()]

        # Lookup/Reference table detection
        if any(word in table_lower for word in ['lookup', 'ref', 'code', 'type', 'status', 'category']):
            classification['type'] = 'lookup'
            classification['confidence'] = 'high'
            classification['reasoning'].append('Contains lookup/reference keywords')

        # Audit/Log table detection
        elif any(word in table_lower for word in ['audit', 'log', 'history', 'trace']):
            classification['type'] = 'audit_log'
            classification['confidence'] = 'high'
            classification['reasoning'].append('Contains audit/log keywords')

        # Junction/Bridge table detection
        elif len(column_names) <= 5 and sum(1 for col in column_names if 'id' in col) >= 2:
            classification['type'] = 'junction'
            classification['confidence'] = 'medium'
            classification['reasoning'].append('Small table with multiple ID columns')

        # Configuration table detection
        elif any(word in table_lower for word in ['config', 'setting', 'parameter', 'option']):
            classification['type'] = 'configuration'
            classification['confidence'] = 'high'
            classification['reasoning'].append('Contains configuration keywords')

        # Main entity table detection
        elif any(col in column_names for col in ['id', 'name', 'title', 'description']):
            classification['type'] = 'entity'
            classification['confidence'] = 'medium'
            classification['reasoning'].append('Has typical entity columns (ID, name, etc.)')

        return classification

    def _analyze_column_semantics(self, column_name: str, column_info: Dict) -> Dict[str, Any]:
        """Analyze column semantics for AI understanding"""
        semantics = {}
        col_lower = column_name.lower()
        data_type = column_info.get('data_type', '').lower()

        # Identify semantic types
        if 'id' in col_lower and data_type in ['int', 'bigint', 'uniqueidentifier']:
            semantics['semantic_type'] = 'identifier'
            semantics['is_likely_primary_key'] = col_lower in ['id', 'pk', 'key']
            semantics['is_likely_foreign_key'] = col_lower.endswith('id') and col_lower != 'id'

        elif any(word in col_lower for word in ['name', 'title', 'label']):
            semantics['semantic_type'] = 'display_name'
            semantics['is_likely_searchable'] = True

        elif any(word in col_lower for word in ['email', 'mail']):
            semantics['semantic_type'] = 'email'
            semantics['validation_pattern'] = 'email'

        elif any(word in col_lower for word in ['phone', 'tel', 'mobile']):
            semantics['semantic_type'] = 'phone'
            semantics['validation_pattern'] = 'phone'

        elif any(word in col_lower for word in ['date', 'time', 'created', 'modified', 'updated']):
            semantics['semantic_type'] = 'timestamp'
            semantics['is_audit_field'] = any(word in col_lower for word in ['created', 'modified', 'updated'])

        elif any(word in col_lower for word in ['status', 'state', 'flag', 'active', 'enabled']):
            semantics['semantic_type'] = 'status_flag'
            semantics['is_likely_enum'] = True

        # Add pattern analysis results if available
        if 'data_patterns' in column_info:
            semantics['detected_patterns'] = column_info['data_patterns']

        return semantics if semantics else {}

    def _suggest_query_patterns(self, table_name: str, details: Dict) -> List[Dict]:
        """Suggest common query patterns for AI"""
        if not self.config.get('export_query_examples', True):
            return []

        patterns = []
        columns = details.get('columns', {})

        # Basic SELECT pattern
        patterns.append({
            'pattern_type': 'basic_select',
            'description': 'Basic SELECT query',
            'sql_template': f"SELECT * FROM [{table_name}]",
            'use_case': 'Retrieve all records'
        })

        # Find ID columns for filtering patterns
        id_columns = [col for col in columns.keys() if col.lower().endswith('id')]
        if id_columns:
            primary_id = id_columns[0]  # Assume first ID column is primary
            patterns.append({
                'pattern_type': 'filter_by_id',
                'description': 'Filter by primary identifier',
                'sql_template': f"SELECT * FROM [{table_name}] WHERE [{primary_id}] = ?",
                'use_case': 'Retrieve specific record by ID'
            })

        # Find name/title columns for search patterns
        name_columns = [col for col in columns.keys() if any(word in col.lower() for word in ['name', 'title', 'description'])]
        if name_columns:
            name_col = name_columns[0]
            patterns.append({
                'pattern_type': 'search_by_name',
                'description': 'Search by name/title',
                'sql_template': f"SELECT * FROM [{table_name}] WHERE [{name_col}] LIKE '%?%'",
                'use_case': 'Search records by name or title'
            })

        # Find date columns for temporal queries
        date_columns = [col for col in columns.keys() if any(word in col.lower() for word in ['date', 'time', 'created', 'modified'])]
        if date_columns:
            date_col = date_columns[0]
            patterns.append({
                'pattern_type': 'filter_by_date',
                'description': 'Filter by date range',
                'sql_template': f"SELECT * FROM [{table_name}] WHERE [{date_col}] BETWEEN ? AND ?",
                'use_case': 'Retrieve records within date range'
            })

        # Status/flag columns for filtering
        status_columns = [col for col in columns.keys() if any(word in col.lower() for word in ['status', 'active', 'enabled', 'flag'])]
        if status_columns:
            status_col = status_columns[0]
            patterns.append({
                'pattern_type': 'filter_by_status',
                'description': 'Filter by status',
                'sql_template': f"SELECT * FROM [{table_name}] WHERE [{status_col}] = ?",
                'use_case': 'Retrieve records by status'
            })

        return patterns

    def _generate_ai_helpful_annotations(self, cursor, table_details: Dict) -> Dict:
        """Generate AI-specific hints about the database structure"""
        if not self.config.get('generate_ai_hints', True):
            return {}

        try:
            logger.info("Analyzing database structure for AI annotations...")

            # Get table metadata for analysis
            table_metadata = self._get_table_metadata_for_analysis(cursor)

            annotations = {
                'likely_lookup_tables': self._identify_lookup_tables(table_metadata, table_details),
                'fact_tables': self._identify_fact_tables(table_metadata, table_details),
                'bridge_tables': self._identify_bridge_tables(table_metadata, table_details),
                'audit_tables': self._identify_audit_tables(table_metadata, table_details),
                'suggested_joins': self._suggest_common_joins(cursor, table_metadata)
            }

            return annotations

        except Exception as e:
            logger.warning(f"Could not generate AI annotations: {e}")
            return {}

    def _get_table_metadata_for_analysis(self, cursor) -> Dict:
        """Get comprehensive table metadata for AI analysis"""
        schema_filter = self._get_schema_filter_clause()

        # Get table information with row counts and foreign key counts
        metadata_query = f"""
            SELECT
                SCHEMA_NAME(t.schema_id) + '.' + t.name AS TableName,
                SUM(p.rows) AS RowCount,
                COUNT(DISTINCT c.column_id) AS ColumnCount,
                COUNT(DISTINCT CASE WHEN c.name LIKE '%ID' OR c.name LIKE '%_ID' THEN c.column_id END) AS IDColumnCount,
                COUNT(DISTINCT fk.constraint_object_id) AS ForeignKeyCount,
                COUNT(DISTINCT pk.constraint_object_id) AS PrimaryKeyCount,
                MAX(CASE WHEN c.name IN ('created_date', 'created_at', 'date_created', 'createdate') THEN 1 ELSE 0 END) AS HasCreatedDate,
                MAX(CASE WHEN c.name IN ('modified_date', 'updated_date', 'modified_at', 'updated_at', 'date_modified', 'modifydate') THEN 1 ELSE 0 END) AS HasModifiedDate,
                MAX(CASE WHEN c.name IN ('deleted_date', 'deleted_at', 'date_deleted', 'is_deleted', 'deleted') THEN 1 ELSE 0 END) AS HasDeletedFlag
            FROM sys.tables t
            JOIN sys.partitions p ON t.object_id = p.object_id AND p.index_id IN (0,1)
            JOIN sys.columns c ON t.object_id = c.object_id
            LEFT JOIN sys.foreign_key_columns fk ON t.object_id = fk.parent_object_id
            LEFT JOIN (
                SELECT tc.parent_object_id, tc.constraint_object_id
                FROM sys.key_constraints tc
                WHERE tc.type = 'PK'
            ) pk ON t.object_id = pk.parent_object_id
            WHERE 1=1 {schema_filter}
            GROUP BY SCHEMA_NAME(t.schema_id), t.name, t.object_id
            ORDER BY RowCount DESC;
        """

        results = self._execute_query_safely(cursor, metadata_query, "Table metadata for AI analysis")

        metadata = {}
        for row in results:
            table_name = row[0]
            metadata[table_name] = {
                'row_count': row[1] or 0,
                'column_count': row[2] or 0,
                'id_column_count': row[3] or 0,
                'foreign_key_count': row[4] or 0,
                'primary_key_count': row[5] or 0,
                'has_created_date': bool(row[6]),
                'has_modified_date': bool(row[7]),
                'has_deleted_flag': bool(row[8])
            }

        return metadata

    def _identify_lookup_tables(self, table_metadata: Dict, table_details: Dict) -> List[Dict]:
        """Identify tables that are likely lookup/reference tables"""
        lookup_tables = []

        for table_name, metadata in table_metadata.items():
            confidence_score = 0
            reasons = []

            # Small number of rows (typically < 1000 for lookup tables)
            if metadata['row_count'] < 1000:
                confidence_score += 30
                reasons.append(f"Small row count ({metadata['row_count']:,} rows)")

            # Few columns (lookup tables are usually simple)
            if metadata['column_count'] <= 5:
                confidence_score += 20
                reasons.append(f"Simple structure ({metadata['column_count']} columns)")

            # Has ID column but few other ID columns (not a bridge table)
            if metadata['id_column_count'] <= 2:
                confidence_score += 15
                reasons.append("Simple ID structure")

            # Naming patterns
            table_lower = table_name.lower()
            if any(word in table_lower for word in ['lookup', 'ref', 'code', 'type', 'status', 'category', 'list']):
                confidence_score += 25
                reasons.append("Contains lookup keywords in name")

            # Check if table has name/description columns (common in lookup tables)
            if table_name in table_details:
                columns = table_details[table_name].get('columns', {})
                name_columns = [col for col in columns.keys() if any(word in col.lower() for word in ['name', 'description', 'label', 'title'])]
                if name_columns:
                    confidence_score += 10
                    reasons.append(f"Has descriptive columns: {', '.join(name_columns[:2])}")

            # If confidence is high enough, classify as lookup table
            if confidence_score >= 40:
                lookup_tables.append({
                    'table_name': table_name,
                    'confidence_score': confidence_score,
                    'row_count': metadata['row_count'],
                    'column_count': metadata['column_count'],
                    'reasons': reasons,
                    'ai_usage_hint': 'Use for data validation, dropdown lists, and reference data joins'
                })

        return sorted(lookup_tables, key=lambda x: x['confidence_score'], reverse=True)

    def _identify_fact_tables(self, table_metadata: Dict, table_details: Dict) -> List[Dict]:
        """Identify tables that are likely fact tables (large tables with many foreign keys)"""
        fact_tables = []

        for table_name, metadata in table_metadata.items():
            confidence_score = 0
            reasons = []

            # Large number of rows
            if metadata['row_count'] > 10000:
                confidence_score += 25
                reasons.append(f"Large dataset ({metadata['row_count']:,} rows)")

                if metadata['row_count'] > 100000:
                    confidence_score += 15
                    reasons.append("Very large dataset")

            # Multiple foreign keys (fact tables reference many dimensions)
            if metadata['foreign_key_count'] >= 3:
                confidence_score += 30
                reasons.append(f"Multiple foreign keys ({metadata['foreign_key_count']})")
            elif metadata['foreign_key_count'] >= 2:
                confidence_score += 20
                reasons.append(f"Several foreign keys ({metadata['foreign_key_count']})")

            # Many ID columns (references to other tables)
            if metadata['id_column_count'] >= 4:
                confidence_score += 20
                reasons.append(f"Many ID columns ({metadata['id_column_count']})")

            # Naming patterns
            table_lower = table_name.lower()
            if any(word in table_lower for word in ['transaction', 'order', 'sale', 'event', 'log', 'record', 'entry', 'fact']):
                confidence_score += 15
                reasons.append("Contains fact table keywords")

            # Has audit fields (common in transactional data)
            if metadata['has_created_date']:
                confidence_score += 10
                reasons.append("Has audit trail (created date)")

            if confidence_score >= 50:
                fact_tables.append({
                    'table_name': table_name,
                    'confidence_score': confidence_score,
                    'row_count': metadata['row_count'],
                    'foreign_key_count': metadata['foreign_key_count'],
                    'reasons': reasons,
                    'ai_usage_hint': 'Primary data source for analytics, aggregations, and business intelligence queries'
                })

        return sorted(fact_tables, key=lambda x: x['confidence_score'], reverse=True)

    def _identify_bridge_tables(self, table_metadata: Dict, table_details: Dict) -> List[Dict]:
        """Identify tables that are likely bridge/junction tables (many-to-many relationships)"""
        bridge_tables = []

        for table_name, metadata in table_metadata.items():
            confidence_score = 0
            reasons = []

            # Small number of columns (bridge tables are usually simple)
            if metadata['column_count'] <= 5:
                confidence_score += 20
                reasons.append(f"Simple structure ({metadata['column_count']} columns)")

            # Multiple ID columns (typically 2+ for bridge tables)
            if metadata['id_column_count'] >= 2:
                confidence_score += 30
                reasons.append(f"Multiple ID columns ({metadata['id_column_count']})")

                if metadata['id_column_count'] >= 3:
                    confidence_score += 10
                    reasons.append("Many ID references")

            # Multiple foreign keys
            if metadata['foreign_key_count'] >= 2:
                confidence_score += 25
                reasons.append(f"Multiple foreign keys ({metadata['foreign_key_count']})")

            # Naming patterns
            table_lower = table_name.lower()
            if any(word in table_lower for word in ['bridge', 'junction', 'link', 'map', 'mapping', 'xref', 'cross']):
                confidence_score += 20
                reasons.append("Contains bridge table keywords")

            # Compound naming (TableA_TableB pattern)
            if '_' in table_name and len(table_name.split('_')) == 2:
                confidence_score += 15
                reasons.append("Compound naming pattern suggests relationship table")

            # No audit fields (bridge tables often don't track creation/modification)
            if not metadata['has_created_date'] and not metadata['has_modified_date']:
                confidence_score += 5
                reasons.append("No audit fields (typical for bridge tables)")

            if confidence_score >= 40:
                bridge_tables.append({
                    'table_name': table_name,
                    'confidence_score': confidence_score,
                    'id_column_count': metadata['id_column_count'],
                    'foreign_key_count': metadata['foreign_key_count'],
                    'reasons': reasons,
                    'ai_usage_hint': 'Use for many-to-many relationship queries and complex joins between entities'
                })

        return sorted(bridge_tables, key=lambda x: x['confidence_score'], reverse=True)

    def _identify_audit_tables(self, table_metadata: Dict, table_details: Dict) -> List[Dict]:
        """Identify tables that are likely audit/history tables"""
        audit_tables = []

        for table_name, metadata in table_metadata.items():
            confidence_score = 0
            reasons = []

            # Has audit fields
            if metadata['has_created_date']:
                confidence_score += 25
                reasons.append("Has created date field")

            if metadata['has_modified_date']:
                confidence_score += 20
                reasons.append("Has modified date field")

            if metadata['has_deleted_flag']:
                confidence_score += 15
                reasons.append("Has soft delete capability")

            # Naming patterns
            table_lower = table_name.lower()
            if any(word in table_lower for word in ['audit', 'history', 'log', 'trace', 'track', 'change']):
                confidence_score += 30
                reasons.append("Contains audit keywords in name")

            # Version or sequence columns
            if table_name in table_details:
                columns = table_details[table_name].get('columns', {})
                version_columns = [col for col in columns.keys() if any(word in col.lower() for word in ['version', 'sequence', 'revision', 'audit'])]
                if version_columns:
                    confidence_score += 15
                    reasons.append(f"Has versioning columns: {', '.join(version_columns[:2])}")

            # Large number of rows (audit tables can grow large)
            if metadata['row_count'] > 50000:
                confidence_score += 10
                reasons.append("Large dataset typical of audit tables")

            if confidence_score >= 35:
                audit_tables.append({
                    'table_name': table_name,
                    'confidence_score': confidence_score,
                    'row_count': metadata['row_count'],
                    'has_created_date': metadata['has_created_date'],
                    'has_modified_date': metadata['has_modified_date'],
                    'has_deleted_flag': metadata['has_deleted_flag'],
                    'reasons': reasons,
                    'ai_usage_hint': 'Use for change tracking, compliance reporting, and historical data analysis'
                })

        return sorted(audit_tables, key=lambda x: x['confidence_score'], reverse=True)

    def _suggest_common_joins(self, cursor, table_metadata: Dict) -> List[Dict]:
        """Suggest common join patterns based on foreign keys and naming conventions"""
        try:
            schema_filter = self._get_schema_filter_clause()

            # Get explicit foreign key relationships
            fk_query = f"""
                SELECT
                    SCHEMA_NAME(tp.schema_id) + '.' + tp.name AS ParentTable,
                    cp.name AS ParentColumn,
                    SCHEMA_NAME(tr.schema_id) + '.' + tr.name AS ReferencedTable,
                    cr.name AS ReferencedColumn,
                    'explicit_foreign_key' AS JoinType,
                    'High' AS Confidence
                FROM sys.foreign_keys fk
                JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
                JOIN sys.tables tp ON fkc.parent_object_id = tp.object_id
                JOIN sys.tables tr ON fkc.referenced_object_id = tr.object_id
                JOIN sys.columns cp ON fkc.parent_object_id = cp.object_id AND fkc.parent_column_id = cp.column_id
                JOIN sys.columns cr ON fkc.referenced_object_id = cr.object_id AND fkc.referenced_column_id = cr.column_id
                WHERE 1=1 {schema_filter.replace('SCHEMA_NAME(t.schema_id)', 'SCHEMA_NAME(tp.schema_id)')}

                UNION ALL

                SELECT
                    t1.TABLE_SCHEMA + '.' + t1.TABLE_NAME AS ParentTable,
                    t1.COLUMN_NAME AS ParentColumn,
                    t2.TABLE_SCHEMA + '.' + t2.TABLE_NAME AS ReferencedTable,
                    t2.COLUMN_NAME AS ReferencedColumn,
                    'naming_convention' AS JoinType,
                    'Medium' AS Confidence
                FROM INFORMATION_SCHEMA.COLUMNS t1
                JOIN INFORMATION_SCHEMA.COLUMNS t2 ON t1.DATA_TYPE = t2.DATA_TYPE
                WHERE t1.TABLE_SCHEMA + '.' + t1.TABLE_NAME != t2.TABLE_SCHEMA + '.' + t2.TABLE_NAME
                AND t1.COLUMN_NAME = t2.COLUMN_NAME + 'ID'
                AND t2.COLUMN_NAME = 'ID'
                AND NOT EXISTS (
                    SELECT 1 FROM sys.foreign_keys fk
                    JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
                    JOIN sys.tables tp ON fkc.parent_object_id = tp.object_id
                    JOIN sys.columns cp ON fkc.parent_object_id = cp.object_id AND fkc.parent_column_id = cp.column_id
                    WHERE SCHEMA_NAME(tp.schema_id) + '.' + tp.name = t1.TABLE_SCHEMA + '.' + t1.TABLE_NAME
                    AND cp.name = t1.COLUMN_NAME
                )
                ORDER BY JoinType, ParentTable;
            """

            results = self._execute_query_safely(cursor, fk_query, "Common join patterns")

            suggested_joins = []
            for row in results:
                parent_table = row[0]
                parent_column = row[1]
                referenced_table = row[2]
                referenced_column = row[3]
                join_type = row[4]
                confidence = row[5]

                # Generate SQL template
                sql_template = f"""
                    SELECT *
                    FROM [{parent_table}] p
                    JOIN [{referenced_table}] r ON p.[{parent_column}] = r.[{referenced_column}]
                """.strip()

                # Determine use case based on table types
                use_case = "Standard entity relationship"
                if any(word in referenced_table.lower() for word in ['lookup', 'ref', 'code', 'type']):
                    use_case = "Lookup/reference data join"
                elif any(word in parent_table.lower() for word in ['order', 'transaction', 'event']):
                    use_case = "Transactional data with reference"

                suggested_joins.append({
                    'parent_table': parent_table,
                    'parent_column': parent_column,
                    'referenced_table': referenced_table,
                    'referenced_column': referenced_column,
                    'join_type': join_type,
                    'confidence': confidence,
                    'sql_template': sql_template,
                    'use_case': use_case,
                    'ai_usage_hint': f"Join {parent_table} with {referenced_table} for complete entity information"
                })

            return suggested_joins[:20]  # Limit to top 20 suggestions

        except Exception as e:
            logger.warning(f"Could not generate join suggestions: {e}")
            return []

    def export_schema(self) -> Dict[str, Any]:
        """Export comprehensive schema information"""
        logger.info("Starting comprehensive schema export...")

        with self._get_connection() as conn:
            cursor = conn.cursor()

            # Get basic schema information
            queries = self._get_comprehensive_queries()
            schema_info = {}

            for section_name, query in queries.items():
                results = self._execute_query_safely(cursor, query, section_name)
                if results:
                    columns = [desc[0] for desc in cursor.description]
                    schema_info[section_name] = [
                        dict(zip(columns, row)) for row in results
                    ]
                else:
                    schema_info[section_name] = []

            # Get table list for additional processing with schema and size filtering
            schema_filter = self._get_schema_filter_clause()

            # Build table selection query with row count filtering for massive table protection
            max_rows_for_selection = self.config.get('max_table_rows_for_selection', 50000000)
            enable_limits = self.config.get('enable_row_count_limits', True)
            prioritize_smaller = self.config.get('prioritize_smaller_tables', True)

            # Row count filter to exclude extremely large tables entirely
            row_count_filter = ""
            if enable_limits:
                row_count_filter = f"HAVING SUM(p.rows) <= {max_rows_for_selection}"

            # Order by size if prioritizing smaller tables
            order_clause = "ORDER BY SUM(p.rows) ASC, t.name" if prioritize_smaller else "ORDER BY t.name"

            table_query = f"""
                SELECT
                    SCHEMA_NAME(t.schema_id) + '.' + t.name AS FullTableName,
                    SUM(p.rows) AS EstimatedRows
                FROM sys.tables t
                JOIN sys.partitions p ON t.object_id = p.object_id
                WHERE p.index_id IN (0,1) {schema_filter}
                GROUP BY SCHEMA_NAME(t.schema_id), t.name
                {row_count_filter}
                {order_clause}
            """
            table_results = self._execute_query_safely(cursor, table_query, "Table list with row counts")

            # Extract table names and row counts
            table_info = [(row[0], row[1]) for row in table_results]

            # Apply table count limit and log information about restrictions
            max_tables = self.config['max_tables_to_sample']
            total_tables_found = len(table_info)

            # Log table size distribution
            if table_info:
                sizes = [row[1] for row in table_info]
                logger.info(f"Found {total_tables_found} tables for processing")
                logger.info(f"Table size range: {min(sizes):,} to {max(sizes):,} rows")

                # Count tables by size categories
                small_tables = sum(1 for size in sizes if size <= 10000)
                medium_tables = sum(1 for size in sizes if 10000 < size <= 100000)
                large_tables = sum(1 for size in sizes if 100000 < size <= 1000000)
                very_large_tables = sum(1 for size in sizes if size > 1000000)

                logger.info(f"Table size distribution: {small_tables} small (≤10K), {medium_tables} medium (10K-100K), "
                           f"{large_tables} large (100K-1M), {very_large_tables} very large (>1M)")

            if len(table_info) > max_tables:
                logger.info(f"Limiting to {max_tables} tables for detailed sampling (performance protection)")
                table_info = table_info[:max_tables]

            # Get sample data and column statistics for each table
            logger.info(f"Collecting sample data and column statistics for {len(table_info)} tables...")
            table_details = {}

            for table_name, estimated_rows in table_info:
                try:
                    # Log table processing with size information
                    logger.info(f"Processing table {table_name} (estimated {estimated_rows:,} rows)")

                    # Check if table is too large for various operations
                    max_sampling_rows = self.config['max_table_rows_for_sampling']
                    max_stats_rows = self.config['max_table_rows_for_stats']

                    skip_sampling = estimated_rows > max_sampling_rows
                    skip_stats = estimated_rows > max_stats_rows

                    # Log what operations will be skipped for large tables
                    if skip_sampling or skip_stats:
                        skipped_ops = []
                        if skip_sampling:
                            skipped_ops.append(f"data sampling (>{max_sampling_rows:,} rows)")
                        if skip_stats:
                            skipped_ops.append(f"column statistics (>{max_stats_rows:,} rows)")
                        logger.info(f"Table {table_name}: Skipping {', '.join(skipped_ops)} due to size ({estimated_rows:,} rows)")

                    # Get sample data (only if table is not too large)
                    sample_data = []
                    if not skip_sampling:
                        sample_data = self._get_table_sample_data(cursor, table_name, self.config['sample_size'])
                    else:
                        logger.info(f"Skipping sample data for {table_name} - too large ({estimated_rows:,} rows)")

                    # Get column information for this table
                    # Split schema and table name for proper querying
                    if '.' in table_name:
                        schema_name, table_only = table_name.split('.', 1)
                    else:
                        schema_name = 'dbo'
                        table_only = table_name

                    col_query = f"""
                        SELECT COLUMN_NAME, DATA_TYPE
                        FROM INFORMATION_SCHEMA.COLUMNS
                        WHERE TABLE_SCHEMA = '{schema_name}' AND TABLE_NAME = '{table_only}'
                        ORDER BY ORDINAL_POSITION
                    """
                    col_results = self._execute_query_safely(cursor, col_query, f"Columns for {table_name}")

                    table_details[table_name] = {
                        'estimated_rows': estimated_rows,
                        'sample_data': [dict(zip([desc[0] for desc in cursor.description], row)) for row in sample_data] if sample_data else [],
                        'columns': {},
                        'sampling_skipped': skip_sampling,
                        'stats_skipped': skip_stats
                    }

                    # Get statistics for each column (only if not too large)
                    for col_name, data_type in col_results:
                        stats = {}
                        distinct_values = []

                        if not skip_stats:
                            stats = self._get_column_statistics(cursor, table_name, col_name)

                            # Get distinct values for categorical columns
                            if (data_type in ['varchar', 'nvarchar', 'char', 'nchar', 'text'] and
                                stats.get('distinct_count', 0) <= self.config['max_distinct_values'] and
                                not skip_sampling):
                                distinct_values = self._get_distinct_values(cursor, table_name, col_name, self.config['max_distinct_values'])
                        else:
                            logger.info(f"Skipping statistics for {table_name}.{col_name} - table too large")

                        # Analyze column patterns for AI insights (only if not too large)
                        patterns = {}
                        if not skip_stats:
                            patterns = self._analyze_column_patterns(cursor, table_name, col_name, data_type)

                        table_details[table_name]['columns'][col_name] = {
                            'data_type': data_type,
                            'statistics': stats,
                            'distinct_values': distinct_values,
                            'patterns': patterns
                        }

                except Exception as e:
                    logger.error(f"Error processing table {table_name}: {e}")
                    continue

            # Generate AI-specific insights
            logger.info("Generating AI-specific insights and metadata...")
            ai_hints = self._generate_ai_hints(table_details)
            detected_relationships = self._detect_table_relationships(cursor)
            ai_annotations = self._generate_ai_helpful_annotations(cursor, table_details)

            # Compile final schema data
            self.schema_data = {
                'export_metadata': {
                    'export_timestamp': self.export_timestamp.isoformat(),
                    'database_server': self.config['server'],
                    'database_name': self.config['database'],
                    'export_version': '2.1',
                    'total_tables_found': len(table_info),
                    'tables_sampled': len(table_details),
                    'max_table_rows_for_sampling': self.config['max_table_rows_for_sampling'],
                    'max_table_rows_for_stats': self.config['max_table_rows_for_stats'],
                    'schema_filter_mode': self.config['schema_filter_mode'],
                    'ai_features_enabled': {
                        'system_stats': self.config.get('include_system_stats', True),
                        'column_patterns': self.config.get('analyze_column_patterns', True),
                        'relationship_detection': self.config.get('detect_table_relationships', True),
                        'ai_hints': self.config.get('generate_ai_hints', True),
                        'query_examples': self.config.get('export_query_examples', True)
                    }
                },
                'schema_information': schema_info,
                'table_details': table_details,
                'ai_insights': ai_hints,
                'detected_relationships': detected_relationships,
                'ai_annotations': ai_annotations
            }

            logger.info("✓ Schema export completed successfully")
            return self.schema_data

    def _save_json_format(self, filename: str) -> None:
        """Save schema data in JSON format"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.schema_data, f, indent=2, default=str, ensure_ascii=False)
            logger.info(f"✓ JSON export saved: {filename}")
        except Exception as e:
            logger.error(f"✗ Failed to save JSON format: {e}")

    def _save_xml_format(self, filename: str) -> None:
        """Save schema data in XML format"""
        try:
            root = ET.Element("DatabaseSchema")

            # Add metadata
            metadata = ET.SubElement(root, "Metadata")
            for key, value in self.schema_data['export_metadata'].items():
                elem = ET.SubElement(metadata, key.replace('_', ''))
                elem.text = str(value)

            # Add schema information
            schema_info = ET.SubElement(root, "SchemaInformation")
            for section_name, data in self.schema_data['schema_information'].items():
                section = ET.SubElement(schema_info, section_name.replace(' ', '').replace('_', ''))
                for item in data:
                    item_elem = ET.SubElement(section, "Item")
                    for key, value in item.items():
                        field = ET.SubElement(item_elem, key.replace(' ', '').replace('_', ''))
                        field.text = str(value) if value is not None else ''

            # Write XML file
            tree = ET.ElementTree(root)
            tree.write(filename, encoding='utf-8', xml_declaration=True)
            logger.info(f"✓ XML export saved: {filename}")
        except Exception as e:
            logger.error(f"✗ Failed to save XML format: {e}")

    def _save_text_format(self, filename: str) -> None:
        """Save schema data in human-readable text format"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                # Write header
                f.write("=" * 80 + "\n")
                f.write("COMPREHENSIVE DATABASE SCHEMA EXPORT\n")
                f.write("=" * 80 + "\n\n")

                # Write metadata
                f.write("EXPORT METADATA:\n")
                f.write("-" * 40 + "\n")
                for key, value in self.schema_data['export_metadata'].items():
                    f.write(f"{key.replace('_', ' ').title()}: {value}\n")
                f.write("\n")

                # Write schema information
                for section_name, data in self.schema_data['schema_information'].items():
                    f.write(f"\n{section_name.upper()}:\n")
                    f.write("-" * len(section_name) + "\n")

                    if data:
                        for item in data:
                            for key, value in item.items():
                                f.write(f"  {key}: {value}\n")
                            f.write("\n")
                    else:
                        f.write("  No data found\n\n")

                # Write table details
                if self.schema_data.get('table_details'):
                    f.write("\nTABLE DETAILS WITH SAMPLE DATA:\n")
                    f.write("=" * 40 + "\n")

                    for table_name, details in self.schema_data['table_details'].items():
                        f.write(f"\nTable: {table_name}\n")
                        f.write("-" * (len(table_name) + 7) + "\n")

                        # Column information
                        f.write("Columns:\n")
                        for col_name, col_info in details['columns'].items():
                            f.write(f"  {col_name} ({col_info['data_type']})\n")
                            if col_info['statistics']:
                                stats = col_info['statistics']
                                f.write(f"    - Total rows: {stats.get('total_rows', 'N/A')}\n")
                                f.write(f"    - Null percentage: {stats.get('null_percentage', 'N/A')}%\n")
                                f.write(f"    - Distinct values: {stats.get('distinct_count', 'N/A')}\n")

                            if col_info['distinct_values']:
                                f.write(f"    - Top values: {', '.join([str(v['value']) for v in col_info['distinct_values'][:5]])}\n")

                        # Sample data
                        if details['sample_data']:
                            f.write("\nSample Data (first 5 rows):\n")
                            for i, row in enumerate(details['sample_data'][:5]):
                                f.write(f"  Row {i+1}: {row}\n")
                        f.write("\n")

            logger.info(f"✓ Text export saved: {filename}")
        except Exception as e:
            logger.error(f"✗ Failed to save text format: {e}")

    def save_exports(self, base_filename: str = None) -> List[str]:
        """Save exports in all configured formats"""
        if not self.schema_data:
            raise SchemaExportError("No schema data to export. Run export_schema() first.")

        if base_filename is None:
            timestamp = self.export_timestamp.strftime("%Y%m%d_%H%M%S")
            base_filename = f"comprehensive_schema_export_{timestamp}"

        saved_files = []

        # Save in all configured formats
        for format_type in self.config['output_formats']:
            filename = f"{base_filename}.{format_type}"

            if format_type == 'json':
                self._save_json_format(filename)
            elif format_type == 'xml':
                self._save_xml_format(filename)
            elif format_type == 'txt':
                self._save_text_format(filename)

            saved_files.append(filename)

            # Create backup on desktop if configured
            if self.config['backup_to_desktop']:
                try:
                    desktop_path = Path.home() / "Desktop"
                    if desktop_path.exists():
                        backup_file = desktop_path / filename
                        if format_type == 'json':
                            self._save_json_format(str(backup_file))
                        elif format_type == 'xml':
                            self._save_xml_format(str(backup_file))
                        elif format_type == 'txt':
                            self._save_text_format(str(backup_file))
                        logger.info(f"✓ Backup saved to desktop: {backup_file}")
                except Exception as e:
                    logger.warning(f"Could not create desktop backup: {e}")

        return saved_files


def main():
    """Main execution function"""
    try:
        logger.info("Starting Comprehensive Schema Export Tool")

        # Initialize exporter
        exporter = ComprehensiveSchemaExporter()

        # Export schema
        schema_data = exporter.export_schema()

        # Save exports
        saved_files = exporter.save_exports()

        # Print summary
        print("\n" + "=" * 60)
        print("EXPORT COMPLETED SUCCESSFULLY")
        print("=" * 60)
        print(f"Database: {exporter.config['database']}")
        print(f"Server: {exporter.config['server']}")
        print(f"Export timestamp: {exporter.export_timestamp}")
        print(f"Total tables processed: {len(schema_data.get('table_details', {}))}")
        print("\nFiles created:")
        for file in saved_files:
            print(f"  ✓ {file}")

        if exporter.config['backup_to_desktop']:
            print(f"\nBackup copies saved to desktop")

        print("\nSchema export completed successfully!")

    except Exception as e:
        logger.error(f"Export failed: {e}")
        print(f"\n✗ Export failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
