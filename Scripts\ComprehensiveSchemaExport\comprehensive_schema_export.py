#!/usr/bin/env python3
"""
Comprehensive SQL Server Schema Export Tool
Exports complete database schema information for AI agent consumption
Includes tables, relationships, constraints, procedures, data samples, and metadata
"""

import pyodbc
import json
import xml.etree.ElementTree as ET
import csv
import os
import sys
import socket
import time
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from contextlib import contextmanager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('schema_export.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class DatabaseConnectionError(Exception):
    """Custom exception for database connection issues"""
    pass

class SchemaExportError(Exception):
    """Custom exception for schema export issues"""
    pass

class ComprehensiveSchemaExporter:
    """Comprehensive database schema exporter with AI-friendly output"""
    
    def __init__(self, config: Optional[Dict] = None):
        """Initialize the schema exporter with configuration"""
        self.config = config or self._get_default_config()
        self.connection = None
        self.schema_data = {}
        self.export_timestamp = datetime.now()
        
    def _get_default_config(self) -> Dict:
        """Get default configuration with environment variable support"""
        return {
            'server': os.getenv('DB_SERVER', 'REMD-ZS-JV6GJV2\\SQLEXPRESS'),
            'database': os.getenv('DB_DATABASE', 'CompleteViewVms'),
            'username': os.getenv('DB_USERNAME', 'sa'),
            'password': os.getenv('DB_PASSWORD', 'Zipper091584!'),
            'drivers': [
                'ODBC Driver 17 for SQL Server',
                'ODBC Driver 13 for SQL Server',
                'SQL Server Native Client 11.0',
                'SQL Server'
            ],
            'connection_timeout': 30,
            'command_timeout': 300,
            'max_retries': 3,
            'retry_delay': 2,
            'output_formats': ['json', 'xml', 'txt'],
            'sample_size': 10,
            'max_distinct_values': 50,
            'backup_to_desktop': True
        }
    
    def _check_dependencies(self) -> bool:
        """Check if required modules are available"""
        try:
            import pyodbc
            logger.info("✓ pyodbc module available")
            return True
        except ImportError as e:
            logger.error(f"✗ Required module missing: {e}")
            logger.error("Install with: pip install pyodbc")
            return False
    
    def _test_tcp_connection(self, server: str, port: int = 1433) -> bool:
        """Test TCP connectivity to SQL Server"""
        try:
            # Extract server name without instance
            server_name = server.split('\\')[0]
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((server_name, port))
            sock.close()
            return result == 0
        except Exception as e:
            logger.warning(f"TCP connection test failed: {e}")
            return False
    
    def _get_connection_string(self, driver: str) -> str:
        """Build connection string with specified driver"""
        return (
            f"DRIVER={{{driver}}};"
            f"SERVER={self.config['server']};"
            f"DATABASE={self.config['database']};"
            f"UID={self.config['username']};"
            f"PWD={self.config['password']};"
            f"Connection Timeout={self.config['connection_timeout']};"
        )
    
    @contextmanager
    def _get_connection(self):
        """Get database connection with fallback drivers and retry logic"""
        if not self._check_dependencies():
            raise DatabaseConnectionError("Required dependencies not available")
        
        # Test TCP connectivity first
        if not self._test_tcp_connection(self.config['server']):
            logger.warning("TCP connectivity test failed, but attempting connection anyway...")
        
        connection = None
        last_error = None
        
        for attempt in range(self.config['max_retries']):
            for driver in self.config['drivers']:
                try:
                    conn_str = self._get_connection_string(driver)
                    logger.info(f"Attempting connection with driver: {driver} (attempt {attempt + 1})")
                    
                    connection = pyodbc.connect(conn_str)
                    connection.timeout = self.config['command_timeout']
                    logger.info(f"✓ Connected successfully with {driver}")
                    
                    try:
                        yield connection
                        return
                    finally:
                        if connection:
                            connection.close()
                            logger.info("Database connection closed")
                    
                except pyodbc.Error as e:
                    last_error = e
                    logger.warning(f"✗ Failed with {driver}: {e}")
                    continue
            
            if attempt < self.config['max_retries'] - 1:
                logger.info(f"Retrying in {self.config['retry_delay']} seconds...")
                time.sleep(self.config['retry_delay'])
        
        raise DatabaseConnectionError(f"Failed to connect after {self.config['max_retries']} attempts. Last error: {last_error}")
    
    def _execute_query_safely(self, cursor, query: str, description: str) -> List[Tuple]:
        """Execute query with error handling and progress indication"""
        try:
            logger.info(f"Executing: {description}")
            start_time = time.time()
            cursor.execute(query)
            results = cursor.fetchall()
            elapsed = time.time() - start_time
            logger.info(f"✓ {description} completed in {elapsed:.2f}s ({len(results)} rows)")
            return results
        except Exception as e:
            logger.error(f"✗ Failed to execute {description}: {e}")
            return []
    
    def _get_comprehensive_queries(self) -> Dict[str, str]:
        """Get all comprehensive schema queries"""
        return {
            # Enhanced table and column information
            "Enhanced Tables and Columns": """
                SELECT 
                    t.TABLE_SCHEMA + '.' + t.TABLE_NAME AS FullTableName,
                    t.COLUMN_NAME,
                    t.DATA_TYPE,
                    t.IS_NULLABLE,
                    t.COLUMN_DEFAULT,
                    t.CHARACTER_MAXIMUM_LENGTH,
                    t.NUMERIC_PRECISION,
                    t.NUMERIC_SCALE,
                    t.ORDINAL_POSITION,
                    CASE WHEN pk.COLUMN_NAME IS NOT NULL THEN 'YES' ELSE 'NO' END AS IS_PRIMARY_KEY,
                    CASE WHEN fk.COLUMN_NAME IS NOT NULL THEN 'YES' ELSE 'NO' END AS IS_FOREIGN_KEY
                FROM INFORMATION_SCHEMA.COLUMNS t
                LEFT JOIN (
                    SELECT ku.TABLE_SCHEMA, ku.TABLE_NAME, ku.COLUMN_NAME
                    FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
                    JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku ON tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
                    WHERE tc.CONSTRAINT_TYPE = 'PRIMARY KEY'
                ) pk ON t.TABLE_SCHEMA = pk.TABLE_SCHEMA AND t.TABLE_NAME = pk.TABLE_NAME AND t.COLUMN_NAME = pk.COLUMN_NAME
                LEFT JOIN (
                    SELECT ku.TABLE_SCHEMA, ku.TABLE_NAME, ku.COLUMN_NAME
                    FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
                    JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku ON tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
                    WHERE tc.CONSTRAINT_TYPE = 'FOREIGN KEY'
                ) fk ON t.TABLE_SCHEMA = fk.TABLE_SCHEMA AND t.TABLE_NAME = fk.TABLE_NAME AND t.COLUMN_NAME = fk.COLUMN_NAME
                ORDER BY t.TABLE_SCHEMA, t.TABLE_NAME, t.ORDINAL_POSITION;
            """,
            
            # Enhanced foreign key relationships
            "Enhanced Foreign Key Relationships": """
                SELECT 
                    fk.name AS FK_ConstraintName,
                    SCHEMA_NAME(tp.schema_id) + '.' + tp.name AS FK_Table,
                    cp.name AS FK_Column,
                    SCHEMA_NAME(tr.schema_id) + '.' + tr.name AS Referenced_Table,
                    cr.name AS Referenced_Column,
                    fk.delete_referential_action_desc AS Delete_Action,
                    fk.update_referential_action_desc AS Update_Action
                FROM sys.foreign_keys fk
                JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
                JOIN sys.tables tp ON fkc.parent_object_id = tp.object_id
                JOIN sys.tables tr ON fkc.referenced_object_id = tr.object_id
                JOIN sys.columns cp ON fkc.parent_object_id = cp.object_id AND fkc.parent_column_id = cp.column_id
                JOIN sys.columns cr ON fkc.referenced_object_id = cr.object_id AND fkc.referenced_column_id = cr.column_id
                ORDER BY FK_Table, FK_Column;
            """,
            
            # Enhanced index information
            "Enhanced Index Information": """
                SELECT 
                    SCHEMA_NAME(t.schema_id) + '.' + t.name AS TableName,
                    i.name AS IndexName,
                    i.type_desc AS IndexType,
                    i.is_unique AS IsUnique,
                    i.is_primary_key AS IsPrimaryKey,
                    i.is_unique_constraint AS IsUniqueConstraint,
                    STRING_AGG(c.name + CASE WHEN ic.is_descending_key = 1 THEN ' DESC' ELSE ' ASC' END, ', ') 
                        WITHIN GROUP (ORDER BY ic.key_ordinal) AS IndexedColumns,
                    i.fill_factor AS FillFactor
                FROM sys.indexes i
                JOIN sys.tables t ON i.object_id = t.object_id
                JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
                JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
                WHERE i.type > 0  -- Exclude heaps
                GROUP BY SCHEMA_NAME(t.schema_id), t.name, i.name, i.type_desc, i.is_unique, 
                         i.is_primary_key, i.is_unique_constraint, i.fill_factor
                ORDER BY TableName, IndexName;
            """,

            # Check constraints with definitions
            "Check Constraints": """
                SELECT
                    SCHEMA_NAME(t.schema_id) + '.' + t.name AS TableName,
                    cc.name AS ConstraintName,
                    cc.definition AS CheckDefinition,
                    cc.is_disabled AS IsDisabled
                FROM sys.check_constraints cc
                JOIN sys.tables t ON cc.parent_object_id = t.object_id
                ORDER BY TableName, ConstraintName;
            """,

            # Views with definitions
            "Views and Definitions": """
                SELECT
                    SCHEMA_NAME(v.schema_id) + '.' + v.name AS ViewName,
                    m.definition AS ViewDefinition,
                    v.create_date AS CreatedDate,
                    v.modify_date AS ModifiedDate
                FROM sys.views v
                JOIN sys.sql_modules m ON v.object_id = m.object_id
                ORDER BY ViewName;
            """,

            # Stored procedures and functions
            "Stored Procedures and Functions": """
                SELECT
                    SCHEMA_NAME(o.schema_id) + '.' + o.name AS ObjectName,
                    o.type_desc AS ObjectType,
                    o.create_date AS CreatedDate,
                    o.modify_date AS ModifiedDate,
                    m.definition AS ObjectDefinition
                FROM sys.objects o
                JOIN sys.sql_modules m ON o.object_id = m.object_id
                WHERE o.type IN ('P', 'FN', 'TF', 'IF', 'PC')
                ORDER BY o.type_desc, ObjectName;
            """,

            # Triggers
            "Triggers": """
                SELECT
                    SCHEMA_NAME(t.schema_id) + '.' + t.name AS TableName,
                    tr.name AS TriggerName,
                    tr.is_disabled AS IsDisabled,
                    CASE tr.is_instead_of_trigger WHEN 1 THEN 'INSTEAD OF' ELSE 'AFTER' END AS TriggerType,
                    m.definition AS TriggerDefinition
                FROM sys.triggers tr
                JOIN sys.tables t ON tr.parent_id = t.object_id
                JOIN sys.sql_modules m ON tr.object_id = m.object_id
                ORDER BY TableName, TriggerName;
            """,

            # User-defined data types
            "User Defined Data Types": """
                SELECT
                    SCHEMA_NAME(t.schema_id) + '.' + t.name AS TypeName,
                    t.system_type_id,
                    st.name AS BaseTypeName,
                    t.max_length,
                    t.precision,
                    t.scale,
                    t.is_nullable
                FROM sys.types t
                JOIN sys.types st ON t.system_type_id = st.system_type_id
                WHERE t.is_user_defined = 1
                ORDER BY TypeName;
            """,

            # Extended properties (descriptions/comments)
            "Extended Properties": """
                SELECT
                    ep.class_desc AS PropertyClass,
                    CASE ep.class
                        WHEN 1 THEN SCHEMA_NAME(o.schema_id) + '.' + o.name
                        ELSE 'Database'
                    END AS ObjectName,
                    CASE ep.minor_id
                        WHEN 0 THEN 'Object'
                        ELSE COL_NAME(ep.major_id, ep.minor_id)
                    END AS PropertyTarget,
                    ep.name AS PropertyName,
                    ep.value AS PropertyValue
                FROM sys.extended_properties ep
                LEFT JOIN sys.objects o ON ep.major_id = o.object_id
                WHERE ep.name = 'MS_Description'
                ORDER BY ObjectName, PropertyTarget;
            """,

            # Table and column statistics
            "Table Statistics": """
                SELECT
                    SCHEMA_NAME(t.schema_id) + '.' + t.name AS TableName,
                    SUM(p.rows) AS RowCount,
                    COUNT(c.column_id) AS ColumnCount,
                    SUM(a.total_pages) * 8 AS TotalSpaceKB,
                    SUM(a.used_pages) * 8 AS UsedSpaceKB,
                    (SUM(a.total_pages) - SUM(a.used_pages)) * 8 AS UnusedSpaceKB
                FROM sys.tables t
                JOIN sys.partitions p ON t.object_id = p.object_id
                JOIN sys.allocation_units a ON p.partition_id = a.container_id
                JOIN sys.columns c ON t.object_id = c.object_id
                WHERE p.index_id IN (0,1)
                GROUP BY SCHEMA_NAME(t.schema_id), t.name
                ORDER BY RowCount DESC;
            """,

            # Object dependencies
            "Object Dependencies": """
                SELECT
                    SCHEMA_NAME(o1.schema_id) + '.' + o1.name AS ReferencingObject,
                    o1.type_desc AS ReferencingType,
                    SCHEMA_NAME(o2.schema_id) + '.' + o2.name AS ReferencedObject,
                    o2.type_desc AS ReferencedType
                FROM sys.sql_expression_dependencies d
                JOIN sys.objects o1 ON d.referencing_id = o1.object_id
                JOIN sys.objects o2 ON d.referenced_id = o2.object_id
                WHERE d.referenced_id IS NOT NULL
                ORDER BY ReferencingObject, ReferencedObject;
            """,

            # Database information
            "Database Information": """
                SELECT
                    DB_NAME() AS DatabaseName,
                    SERVERPROPERTY('ProductVersion') AS SQLServerVersion,
                    SERVERPROPERTY('Edition') AS SQLServerEdition,
                    SERVERPROPERTY('Collation') AS ServerCollation,
                    d.collation_name AS DatabaseCollation,
                    d.create_date AS DatabaseCreated,
                    d.compatibility_level AS CompatibilityLevel
                FROM sys.databases d
                WHERE d.name = DB_NAME();
            """
        }

    def _get_table_sample_data(self, cursor, table_name: str, sample_size: int = 10) -> List[Tuple]:
        """Get sample data from a table"""
        try:
            # Use TABLESAMPLE for large tables, TOP for smaller ones
            # Properly bracket table name for safety
            query = f"""
                SELECT TOP {sample_size} *
                FROM [{table_name}]
                ORDER BY (SELECT NULL)  -- Random order
            """
            return self._execute_query_safely(cursor, query, f"Sample data from {table_name}")
        except Exception as e:
            logger.warning(f"Could not get sample data from {table_name}: {e}")
            return []

    def _get_column_statistics(self, cursor, table_name: str, column_name: str) -> Dict:
        """Get statistics for a specific column"""
        try:
            # Get basic statistics with properly bracketed identifiers
            stats_query = f"""
                SELECT
                    COUNT(*) as total_count,
                    COUNT([{column_name}]) as non_null_count,
                    COUNT(DISTINCT [{column_name}]) as distinct_count
                FROM [{table_name}]
            """
            stats_result = self._execute_query_safely(cursor, stats_query, f"Statistics for {table_name}.{column_name}")

            if stats_result:
                total, non_null, distinct = stats_result[0]
                null_count = total - non_null
                null_percentage = (null_count / total * 100) if total > 0 else 0

                return {
                    'total_rows': total,
                    'non_null_count': non_null,
                    'null_count': null_count,
                    'null_percentage': round(null_percentage, 2),
                    'distinct_count': distinct,
                    'uniqueness_ratio': round(distinct / non_null * 100, 2) if non_null > 0 else 0
                }
        except Exception as e:
            logger.warning(f"Could not get statistics for {table_name}.{column_name}: {e}")

        return {}

    def _get_distinct_values(self, cursor, table_name: str, column_name: str, max_values: int = 50) -> List:
        """Get distinct values for categorical columns"""
        try:
            # Properly bracket identifiers for safety with special characters and reserved words
            query = f"""
                SELECT TOP {max_values}
                    [{column_name}],
                    COUNT(*) as frequency
                FROM [{table_name}]
                WHERE [{column_name}] IS NOT NULL
                GROUP BY [{column_name}]
                ORDER BY COUNT(*) DESC
            """
            results = self._execute_query_safely(cursor, query, f"Distinct values for {table_name}.{column_name}")
            return [{'value': row[0], 'frequency': row[1]} for row in results]
        except Exception as e:
            logger.warning(f"Could not get distinct values for {table_name}.{column_name}: {e}")
            return []

    def export_schema(self) -> Dict[str, Any]:
        """Export comprehensive schema information"""
        logger.info("Starting comprehensive schema export...")

        with self._get_connection() as conn:
            cursor = conn.cursor()

            # Get basic schema information
            queries = self._get_comprehensive_queries()
            schema_info = {}

            for section_name, query in queries.items():
                results = self._execute_query_safely(cursor, query, section_name)
                if results:
                    columns = [desc[0] for desc in cursor.description]
                    schema_info[section_name] = [
                        dict(zip(columns, row)) for row in results
                    ]
                else:
                    schema_info[section_name] = []

            # Get table list for additional processing
            table_query = """
                SELECT SCHEMA_NAME(schema_id) + '.' + name AS FullTableName
                FROM sys.tables
                ORDER BY name
            """
            table_results = self._execute_query_safely(cursor, table_query, "Table list")
            tables = [row[0] for row in table_results]

            # Get sample data and column statistics for each table
            logger.info("Collecting sample data and column statistics...")
            table_details = {}

            for table in tables[:10]:  # Limit to first 10 tables for performance
                try:
                    # Get sample data
                    sample_data = self._get_table_sample_data(cursor, table, self.config['sample_size'])

                    # Get column information for this table
                    col_query = f"""
                        SELECT COLUMN_NAME, DATA_TYPE
                        FROM INFORMATION_SCHEMA.COLUMNS
                        WHERE TABLE_SCHEMA + '.' + TABLE_NAME = '{table}'
                        ORDER BY ORDINAL_POSITION
                    """
                    col_results = self._execute_query_safely(cursor, col_query, f"Columns for {table}")

                    table_details[table] = {
                        'sample_data': [dict(zip([desc[0] for desc in cursor.description], row)) for row in sample_data] if sample_data else [],
                        'columns': {}
                    }

                    # Get statistics for each column
                    for col_name, data_type in col_results:
                        stats = self._get_column_statistics(cursor, table, col_name)

                        # Get distinct values for categorical columns
                        distinct_values = []
                        if data_type in ['varchar', 'nvarchar', 'char', 'nchar', 'text'] and stats.get('distinct_count', 0) <= self.config['max_distinct_values']:
                            distinct_values = self._get_distinct_values(cursor, table, col_name, self.config['max_distinct_values'])

                        table_details[table]['columns'][col_name] = {
                            'data_type': data_type,
                            'statistics': stats,
                            'distinct_values': distinct_values
                        }

                except Exception as e:
                    logger.error(f"Error processing table {table}: {e}")
                    continue

            # Compile final schema data
            self.schema_data = {
                'export_metadata': {
                    'export_timestamp': self.export_timestamp.isoformat(),
                    'database_server': self.config['server'],
                    'database_name': self.config['database'],
                    'export_version': '2.0',
                    'total_tables': len(tables)
                },
                'schema_information': schema_info,
                'table_details': table_details
            }

            logger.info("✓ Schema export completed successfully")
            return self.schema_data

    def _save_json_format(self, filename: str) -> None:
        """Save schema data in JSON format"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.schema_data, f, indent=2, default=str, ensure_ascii=False)
            logger.info(f"✓ JSON export saved: {filename}")
        except Exception as e:
            logger.error(f"✗ Failed to save JSON format: {e}")

    def _save_xml_format(self, filename: str) -> None:
        """Save schema data in XML format"""
        try:
            root = ET.Element("DatabaseSchema")

            # Add metadata
            metadata = ET.SubElement(root, "Metadata")
            for key, value in self.schema_data['export_metadata'].items():
                elem = ET.SubElement(metadata, key.replace('_', ''))
                elem.text = str(value)

            # Add schema information
            schema_info = ET.SubElement(root, "SchemaInformation")
            for section_name, data in self.schema_data['schema_information'].items():
                section = ET.SubElement(schema_info, section_name.replace(' ', '').replace('_', ''))
                for item in data:
                    item_elem = ET.SubElement(section, "Item")
                    for key, value in item.items():
                        field = ET.SubElement(item_elem, key.replace(' ', '').replace('_', ''))
                        field.text = str(value) if value is not None else ''

            # Write XML file
            tree = ET.ElementTree(root)
            tree.write(filename, encoding='utf-8', xml_declaration=True)
            logger.info(f"✓ XML export saved: {filename}")
        except Exception as e:
            logger.error(f"✗ Failed to save XML format: {e}")

    def _save_text_format(self, filename: str) -> None:
        """Save schema data in human-readable text format"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                # Write header
                f.write("=" * 80 + "\n")
                f.write("COMPREHENSIVE DATABASE SCHEMA EXPORT\n")
                f.write("=" * 80 + "\n\n")

                # Write metadata
                f.write("EXPORT METADATA:\n")
                f.write("-" * 40 + "\n")
                for key, value in self.schema_data['export_metadata'].items():
                    f.write(f"{key.replace('_', ' ').title()}: {value}\n")
                f.write("\n")

                # Write schema information
                for section_name, data in self.schema_data['schema_information'].items():
                    f.write(f"\n{section_name.upper()}:\n")
                    f.write("-" * len(section_name) + "\n")

                    if data:
                        for item in data:
                            for key, value in item.items():
                                f.write(f"  {key}: {value}\n")
                            f.write("\n")
                    else:
                        f.write("  No data found\n\n")

                # Write table details
                if self.schema_data.get('table_details'):
                    f.write("\nTABLE DETAILS WITH SAMPLE DATA:\n")
                    f.write("=" * 40 + "\n")

                    for table_name, details in self.schema_data['table_details'].items():
                        f.write(f"\nTable: {table_name}\n")
                        f.write("-" * (len(table_name) + 7) + "\n")

                        # Column information
                        f.write("Columns:\n")
                        for col_name, col_info in details['columns'].items():
                            f.write(f"  {col_name} ({col_info['data_type']})\n")
                            if col_info['statistics']:
                                stats = col_info['statistics']
                                f.write(f"    - Total rows: {stats.get('total_rows', 'N/A')}\n")
                                f.write(f"    - Null percentage: {stats.get('null_percentage', 'N/A')}%\n")
                                f.write(f"    - Distinct values: {stats.get('distinct_count', 'N/A')}\n")

                            if col_info['distinct_values']:
                                f.write(f"    - Top values: {', '.join([str(v['value']) for v in col_info['distinct_values'][:5]])}\n")

                        # Sample data
                        if details['sample_data']:
                            f.write("\nSample Data (first 5 rows):\n")
                            for i, row in enumerate(details['sample_data'][:5]):
                                f.write(f"  Row {i+1}: {row}\n")
                        f.write("\n")

            logger.info(f"✓ Text export saved: {filename}")
        except Exception as e:
            logger.error(f"✗ Failed to save text format: {e}")

    def save_exports(self, base_filename: str = None) -> List[str]:
        """Save exports in all configured formats"""
        if not self.schema_data:
            raise SchemaExportError("No schema data to export. Run export_schema() first.")

        if base_filename is None:
            timestamp = self.export_timestamp.strftime("%Y%m%d_%H%M%S")
            base_filename = f"comprehensive_schema_export_{timestamp}"

        saved_files = []

        # Save in all configured formats
        for format_type in self.config['output_formats']:
            filename = f"{base_filename}.{format_type}"

            if format_type == 'json':
                self._save_json_format(filename)
            elif format_type == 'xml':
                self._save_xml_format(filename)
            elif format_type == 'txt':
                self._save_text_format(filename)

            saved_files.append(filename)

            # Create backup on desktop if configured
            if self.config['backup_to_desktop']:
                try:
                    desktop_path = Path.home() / "Desktop"
                    if desktop_path.exists():
                        backup_file = desktop_path / filename
                        if format_type == 'json':
                            self._save_json_format(str(backup_file))
                        elif format_type == 'xml':
                            self._save_xml_format(str(backup_file))
                        elif format_type == 'txt':
                            self._save_text_format(str(backup_file))
                        logger.info(f"✓ Backup saved to desktop: {backup_file}")
                except Exception as e:
                    logger.warning(f"Could not create desktop backup: {e}")

        return saved_files


def main():
    """Main execution function"""
    try:
        logger.info("Starting Comprehensive Schema Export Tool")

        # Initialize exporter
        exporter = ComprehensiveSchemaExporter()

        # Export schema
        schema_data = exporter.export_schema()

        # Save exports
        saved_files = exporter.save_exports()

        # Print summary
        print("\n" + "=" * 60)
        print("EXPORT COMPLETED SUCCESSFULLY")
        print("=" * 60)
        print(f"Database: {exporter.config['database']}")
        print(f"Server: {exporter.config['server']}")
        print(f"Export timestamp: {exporter.export_timestamp}")
        print(f"Total tables processed: {len(schema_data.get('table_details', {}))}")
        print("\nFiles created:")
        for file in saved_files:
            print(f"  ✓ {file}")

        if exporter.config['backup_to_desktop']:
            print(f"\nBackup copies saved to desktop")

        print("\nSchema export completed successfully!")

    except Exception as e:
        logger.error(f"Export failed: {e}")
        print(f"\n✗ Export failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
