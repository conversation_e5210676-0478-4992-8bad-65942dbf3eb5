import pyodbc

conn_str = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=REMD-ZS-JV6GJV2\SQLEXPRESS;"
    "DATABASE=CompleteViewVms;"
    "UID=sa;"
    "PWD=Zipper091584!"
)

queries = {
    "Tables and Columns": """
        SELECT TABLE_NAME, COLUMN_NAME, DATA_TYPE
        FROM INFORMATION_SCHEMA.COLUMNS
        ORDER BY TABLE_NAME, ORDINAL_POSITION;
    """,
    "Primary Keys": """
        SELECT KU.TABLE_NAME, KU.COLUMN_NAME
        FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS TC
        JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE KU 
            ON TC.CONSTRAINT_NAME = KU.CONSTRAINT_NAME
        WHERE TC.CONSTRAINT_TYPE = 'PRIMARY KEY'
        ORDER BY KU.TABLE_NAME;
    """,
    "Foreign Keys": """
        SELECT 
            FK.TABLE_NAME AS FK_Table,
            CU.COLUMN_NAME AS FK_Column,
            PK.TABLE_NAME AS PK_Table,
            PT.COLUMN_NAME AS PK_Column
        FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS C
        JOIN INFORMATION_SCHEMA.TABLE_CONSTRAINTS FK 
            ON C.CONSTRAINT_NAME = FK.CONSTRAINT_NAME
        JOIN INFORMATION_SCHEMA.TABLE_CONSTRAINTS PK 
            ON C.UNIQUE_CONSTRAINT_NAME = PK.CONSTRAINT_NAME
        JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE CU 
            ON C.CONSTRAINT_NAME = CU.CONSTRAINT_NAME
        JOIN (
            SELECT 
                i1.TABLE_NAME, 
                i2.COLUMN_NAME
            FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS i1
            JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE i2 
                ON i1.CONSTRAINT_NAME = i2.CONSTRAINT_NAME
            WHERE i1.CONSTRAINT_TYPE = 'PRIMARY KEY'
        ) PT ON PT.TABLE_NAME = PK.TABLE_NAME
        ORDER BY FK.TABLE_NAME;
    """,
    "Indexes": """
        SELECT 
            t.name AS TableName,
            ind.name AS IndexName,
            col.name AS ColumnName
        FROM sys.indexes ind 
        INNER JOIN sys.index_columns ic ON ind.object_id = ic.object_id AND ind.index_id = ic.index_id
        INNER JOIN sys.columns col ON ic.object_id = col.object_id AND ic.column_id = col.column_id
        INNER JOIN sys.tables t ON ind.object_id = t.object_id
        WHERE ind.is_primary_key = 0 AND ind.is_unique_constraint = 0;
    """,
    "Row Counts": """
        SELECT 
            t.NAME AS TableName,
            SUM(p.rows) AS RowCounts
        FROM 
            sys.tables t
        INNER JOIN 
            sys.partitions p ON t.object_id = p.object_id
        WHERE 
            p.index_id IN (0,1)
        GROUP BY 
            t.Name
        ORDER BY 
            RowCounts DESC;
    """
}

output_file = "sql_server_schema_export.txt"

with open(output_file, "w", encoding="utf-8") as f:
    try:
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()

        for label, sql in queries.items():
            f.write(f"\n--- {label} ---\n")
            cursor.execute(sql)
            columns = [desc[0] for desc in cursor.description]
            for row in cursor.fetchall():
                f.write(", ".join(f"{col}: {val}" for col, val in zip(columns, row)) + "\n")

        print(f"Export complete: {output_file}")
    except Exception as e:
        print("Error:", e)
